@if($customFields)
    @foreach($customFields as $customField)
        @php
            $fieldName = 'custom_field[' . $customField->id . ']';
            $label = Form::label('custom_field-'.$customField->id, __($customField->name), ['class' => 'form-label']);
            $fieldValue = isset($customFieldValues[$customField->id]) ? $customFieldValues[$customField->id] : null;
            $isRequired = $customField->is_required;
        @endphp

        <div class="col-lg-4 col-md-4 col-sm-6 col-12">
            <div class="form-group">
                {!! $label !!}
                @if($isRequired)
                    <x-required></x-required>
                @endif
                <div class="input-group">
                    
                    @switch($customField->type)

                        @case('text')
                            {{ Form::text($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('email')
                            {{ Form::email($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('number')
                            {{ Form::number($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('date')
                            {{ Form::date($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('datetime')
                            {{ Form::datetimeLocal($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('textarea')
                            {{ Form::textarea($fieldName, $fieldValue, ['class' => 'form-control', 'rows' => 1, 'required' => $isRequired ? 'required' : null]) }}
                            @break

                            
                            @case('checkbox')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @php $selectedValues = is_array($fieldValue) ? $fieldValue : (is_string($fieldValue) ? json_decode($fieldValue, true) : []); @endphp
    @foreach($options as $option)
        <div class="form-check">
            {{ Form::checkbox($fieldName.'[]', $option, in_array($option, $selectedValues), ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index, 'required' => $isRequired ? 'required' : null]) }}
            {{ Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
        </div>
    @endforeach
    @break

@case('radio')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @foreach($options as $option)
        <div class="form-check">
            {{ Form::radio($fieldName, $option, $fieldValue == $option, ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index, 'required' => $isRequired ? 'required' : null]) }}
            {{ Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
        </div>
    @endforeach
    @break

@case('select')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @php $selectOptions = ['' => 'Select an option']; @endphp
    @if($options)
        @foreach($options as $option)
            @php $selectOptions[$option] = $option; @endphp
        @endforeach
    @endif
    {{ Form::select($fieldName, $selectOptions, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
    @break

@case('multiselect')
    @php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); @endphp
    @php $selectOptions = []; @endphp
    @php $selectedValues = is_array($fieldValue) ? $fieldValue : (is_string($fieldValue) ? json_decode($fieldValue, true) : []); @endphp
    @if($options)
        @foreach($options as $option)
            @php $selectOptions[$option] = $option; @endphp
        @endforeach
    @endif
    {{ Form::select($fieldName.'[]', $selectOptions, $selectedValues, ['class' => 'form-control', 'multiple' => 'multiple', 'size' => '4', 'required' => $isRequired ? 'required' : null]) }}
    <small class="form-text text-muted">{{ __('Hold Ctrl (Cmd on Mac) to select multiple options') }}</small>
    @break

                        @case('link')
                            {{ Form::url($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('color')
                             {{ Form::input('color', $fieldName, $fieldValue ?: '#000000', ['class' => 'form-control form-control-color', 'title' => 'Pick a color', 'required' => $isRequired ? 'required' : null]) }}
                            @break

                        @case('file')
                            {{ Form::file($fieldName, ['class' => 'form-control', 'accept' => '*/*', 'required' => $isRequired ? 'required' : null]) }}
                            @if($fieldValue)
                                <small class="form-text text-muted">{{ __('Current file:') }} {{ $fieldValue }}</small>
                            @endif
                            @break

                        @case('file_multiple')
                            {{ Form::file($fieldName.'[]', ['class' => 'form-control', 'multiple' => 'multiple', 'accept' => '*/*', 'required' => $isRequired ? 'required' : null]) }}
                            <small class="form-text text-muted">{{ __('You can select multiple files') }}</small>
                            @if($fieldValue)
                                <small class="form-text text-muted">{{ __('Current files:') }} {{ is_array($fieldValue) ? implode(', ', $fieldValue) : $fieldValue }}</small>
                            @endif
                            @break

                        @default
                            <span class="text-danger">{{ __('Unknown Field Type') }}</span>
                    @endswitch
                </div>
            </div>
        </div>
    @endforeach
@endif
