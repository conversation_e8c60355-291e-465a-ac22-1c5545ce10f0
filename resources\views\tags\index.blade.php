@extends('layouts.admin')
@section('page-title')
    {{__('Manage Tags')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Tags')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="#" data-url="{{ route('tags.create') }}" data-bs-toggle="tooltip" title="{{__('Create')}}" data-ajax-popup="true" data-title="{{__('Create New Tag')}}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header p-3">
                    <h5>{{ __('Tag Management') }}</h5>
                    <small class="text-muted">{{ __('Manage tags for leads, projects, and deals') }}</small>
                </div>
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-md-12">
                            <!-- Enhanced Filters Section -->
                           

                            <!-- Enhanced Action Bar -->
                            <div class="d-flex justify-content-between align-items-center mb-5">
                                <div class="d-flex align-items-center">
                                    <div class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill">
                                        <i class="ti ti-tags me-1"></i>{{ __('Tags Management') }}
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="#" class="btn btn-primary px-4 py-2 rounded-pill shadow-sm" data-url="{{ route('tags.create') }}" data-ajax-popup="true" data-title="{{ __('Create New Tag') }}">
                                        <i class="ti ti-plus me-2"></i>{{ __('Create Tag') }}
                                    </a>
                                </div>
                            </div>

                            <!-- Bulk Actions Bar -->
                            <div id="bulk-actions-bar" class="card mb-3 border-0 shadow-sm" style="display: none;">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center gap-3">
                                            <span class="text-muted fw-medium">
                                                <i class="ti ti-check me-1"></i>
                                                <span id="selected-count">0</span> {{ __('tags selected') }}
                                            </span>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-selection">
                                                <i class="ti ti-x me-1"></i>{{ __('Clear Selection') }}
                                            </button>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-settings me-1"></i>{{ __('Bulk Actions') }}
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" id="bulk-activate">
                                                        <i class="ti ti-eye me-2"></i>{{ __('Activate Selected') }}
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#" id="bulk-deactivate">
                                                        <i class="ti ti-eye-off me-2"></i>{{ __('Deactivate Selected') }}
                                                    </a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" id="bulk-delete">
                                                        <i class="ti ti-trash me-2"></i>{{ __('Delete Selected') }}
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover border-0 shadow-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0 py-3 px-4 fw-semibold text-center" width="50px">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="select-all">
                                                </div>
                                            </th>
                                            <th class="border-0 py-3 px-4 fw-semibold">{{ __('Tag Name') }}</th>
                                            <th class="border-0 py-3 px-4 fw-semibold text-center">{{ __('Color') }}</th>
                                            <th class="border-0 py-3 px-4 fw-semibold text-center">{{ __('Usage') }}</th>
                                            <th class="border-0 py-3 px-4 fw-semibold text-center">{{ __('Status') }}</th>
                                            <th class="border-0 py-3 px-4 fw-semibold text-center" width="200px">{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($tags ?? [] as $tag)
                                            <tr class="border-bottom">
                                                <td class="py-3 px-4 text-center align-middle">
                                                    <div class="form-check">
                                                        <input class="form-check-input row-checkbox" type="checkbox" 
                                                               value="{{ $tag->id }}" 
                                                               id="tag-{{ $tag->id }}">
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4 align-middle">
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary-subtle rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <i class="ti ti-tag text-primary"></i>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0 fw-medium">{{ $tag->name }}</h6>
                                                            <small class="text-muted">ID: {{ $tag->id }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4 text-center align-middle">
                                                    <div class="chip-chip bg-{{ $tag->color }}-subtle text-{{ $tag->color }}">
                                                        <i class="ti ti-palette me-1"></i>
                                                        <span class="chip-text">{{ ucfirst($tag->color) }}</span>
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4 text-center align-middle">
                                                    <div class="d-flex justify-content-center gap-2">
                                                        @php
                                                            $leadCount = $tag->leads()->count();
                                                            $projectCount = $tag->projects()->count();
                                                         
                                                            $totalUsage = $leadCount + $projectCount;
                                                        @endphp
                                                        <div class="badge bg-info-subtle text-info">
                                                            <i class="ti ti-users me-1"></i>{{ $leadCount }} {{ __('Leads') }}
                                                        </div>
                                                        <div class="badge bg-warning-subtle text-warning">
                                                            <i class="ti ti-briefcase me-1"></i>{{ $projectCount }} {{ __('Projects') }}
                                                        </div>
                                                       
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4 text-center align-middle">
                                                    <div class="form-check form-switch d-flex justify-content-center">
                                                        <input class="form-check-input status-toggle" type="checkbox" 
                                                               id="status-{{ $tag->id }}" 
                                                               data-tag-id="{{ $tag->id }}"
                                                               {{ $tag->is_active ? 'checked' : '' }}
                                                               style="width: 3rem; height: 1.5rem;">
                                                        <label class="form-check-label ms-2 fw-medium" for="status-{{ $tag->id }}">
                                                            {{ $tag->is_active ? __('Active') : __('Inactive') }}
                                                        </label>
                                                    </div>
                                                </td>
                                                                                    <td class="py-3 px-4 text-center align-middle">
                                        <div class="d-flex justify-content-center gap-2">
                                            <a href="#" class="btn btn-outline-primary btn-sm px-3" 
                                               data-url="{{ route('tags.edit', $tag->id) }}" 
                                               data-ajax-popup="true" 
                                               data-title="{{ __('Edit Tag') }}"
                                               title="{{ __('Edit') }}">
                                                <i class="ti ti-pencil me-1"></i>{{ __('Edit') }}
                                            </a>
                                            {!! Form::open(['method' => 'DELETE', 'route' => ['tags.destroy', $tag->id], 'style' => 'display:inline']) !!}
                                                <button type="submit" class="btn btn-outline-danger btn-sm px-3 delete-tag-btn" 
                                                        data-tag-id="{{ $tag->id }}"
                                                        title="{{ __('Delete') }}">
                                                    <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                                                </button>
                                            {!! Form::close() !!}
                                        </div>
                                    </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center py-5">
                                                    <div class="text-muted">
                                                        <i class="ti ti-tags fs-1 mb-3 d-block"></i>
                                                        <h5>{{ __('No tags found') }}</h5>
                                                        <p>{{ __('Create your first tag to get started') }}</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {
    // Status toggle functionality
    $('.status-toggle').on('change', function() {
        const tagId = $(this).data('tag-id');
        const isActive = $(this).is(':checked');
        
        $.ajax({
            url: '{{ route("tags.toggle-status", ":tag") }}'.replace(':tag', tagId),
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // Update the label
                    const label = $('label[for="status-' + tagId + '"]');
                    label.text(isActive ? '{{ __("Active") }}' : '{{ __("Inactive") }}');
                    
                    // Show success message
                    toastr.success(response.success);
                }
            },
            error: function(xhr) {
                // Revert the toggle if there's an error
                const checkbox = $('#status-' + tagId);
                checkbox.prop('checked', !isActive);
                
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    toastr.error(xhr.responseJSON.error);
                } else {
                    toastr.error('{{ __("An error occurred while updating the tag status") }}');
                }
            }
        });
    });

    // Delete confirmation
    $('.delete-tag-btn').on('click', function(e) {
        e.preventDefault();
        const tagId = $(this).data('tag-id');
        const form = $(this).closest('form');
        
        if (confirm('{{ __("Are you sure you want to delete this tag? This action cannot be undone.") }}')) {
            form.submit();
        }
    });

    // Filter functionality
    $('#apply-filters').on('click', function() {
        applyFilters();
    });

    $('#clear-filters').on('click', function() {
        $('#search-filter').val('');
        $('#color-filter').val('all');
        $('#status-filter').val('all');
        applyFilters();
    });

    function applyFilters() {
        const search = $('#search-filter').val().toLowerCase();
        const color = $('#color-filter').val();
        const status = $('#status-filter').val();
        
        $('tbody tr').each(function() {
            const row = $(this);
            const tagName = row.find('h6').text().toLowerCase();
            const tagColor = row.find('.chip-chip').text().toLowerCase();
            const tagStatus = row.find('.status-toggle').is(':checked') ? 'active' : 'inactive';
            
            let show = true;
            
            if (search && !tagName.includes(search)) {
                show = false;
            }
            
            if (color !== 'all' && !tagColor.includes(color)) {
                show = false;
            }
            
            if (status !== 'all' && tagStatus !== status) {
                show = false;
            }
            
            row.toggle(show);
        });
        
        updateFilterResults();
    }

    function updateFilterResults() {
        const visibleRows = $('tbody tr:visible').length;
        const totalRows = $('tbody tr').length;
        $('#filter-results').text(`${visibleRows} of ${totalRows} tags`);
    }

    // Select all functionality
    $('#select-all').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.row-checkbox').prop('checked', isChecked);
        updateBulkActions();
    });

    $('.row-checkbox').on('change', function() {
        updateBulkActions();
    });

    function updateBulkActions() {
        const checkedBoxes = $('.row-checkbox:checked');
        const totalBoxes = $('.row-checkbox').length;
        
        if (checkedBoxes.length > 0) {
            $('#bulk-actions-bar').show();
            $('#selected-count').text(checkedBoxes.length);
            
            // Update select all checkbox
            if (checkedBoxes.length === totalBoxes) {
                $('#select-all').prop('indeterminate', false).prop('checked', true);
            } else {
                $('#select-all').prop('indeterminate', true).prop('checked', false);
            }
        } else {
            $('#bulk-actions-bar').hide();
            $('#select-all').prop('indeterminate', false).prop('checked', false);
        }
    }

    $('#clear-selection').on('click', function() {
        $('.row-checkbox, #select-all').prop('checked', false);
        updateBulkActions();
    });

    // Initialize filter results
    updateFilterResults();
});
</script>
@endpush 