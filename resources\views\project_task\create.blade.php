{{ Form::open(['route' => ['projects.tasks.store',$project_id,$stage_id],'id' => 'create_task', 'class'=>'needs-validation', 'novalidate']) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['project task']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('name', __('Task name'),['class' => 'form-label']) }}<x-required></x-required>
                {{ Form::text('name', null, ['class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Task Name')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('milestone_id', __('Milestone'),['class' => 'form-label']) }}
                <select class="form-control select" name="milestone_id" id="milestone_id">
                    <option value="0" class="text-muted">{{__('Select Milestone')}}</option>
                    @foreach($project->milestones as $m_val)
                        <option value="{{ $m_val->id }}">{{ $m_val->title }}</option>
                    @endforeach
                </select>
                <div class="text-xs mt-1">
                    {{ __('Create milestone here.') }} <a href="{{ route('projects.show', $project_id) }}"><b>{{ __('Create milestone') }}</b></a>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                {{ Form::label('description', __('Description'),['class' => 'form-label']) }}
                <small class="form-text text-muted mb-2 mt-0">{{__('This textarea will autosize while you type')}}</small>
                {{ Form::textarea('description', null, ['class' => 'form-control','rows'=>'1','data-toggle' => 'autosize', 'placeholder'=>__('Enter Description')]) }}
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                {{ Form::label('tags', __('Tags'), ['class' => 'form-label']) }}
                {{ Form::select('tags[]', $tags ?? [], null, ['class' => 'form-control select2', 'multiple' => 'multiple', 'id' => 'tags-select', 'data-placeholder' => __('Select or create tags...')]) }}
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ __('You can select existing tags or type to create new ones. New tags will be created automatically.') }}
                </small>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('estimated_hrs', __('Estimated Hours'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('allocated total ').$hrs['allocated'].__(' hrs in other tasks')}}</small>
                {{ Form::number('estimated_hrs', null, ['class' => 'form-control','required' => 'required','min'=>'0','maxlength' => '8', 'placeholder'=>__('Enter Estimated Hours')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('priority', __('Priority'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('Set Priority of your task')}}</small>
                <select class="form-control select" name="priority" id="priority" required>
                    @foreach(\App\Models\ProjectTask::$priority as $key => $val)
                        <option value="{{ $key }}">{{ __($val) }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('start_date', __('Start Date'),['class' => 'form-label']) }}
                {{ Form::date('start_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('end_date', __('End Date'),['class' => 'form-label']) }}
                {{ Form::date('end_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="form-label">{{__('Task members')}}</label>
        <small class="form-text text-muted mb-2 mt-0">{{__('Select users to assign this task to.')}}</small>
    </div>

    @if($project->users->count() > 0)
        <div class="form-group">
            <select class="form-control select2" name="assign_to[]" id="assign_to_members" multiple="multiple" data-placeholder="{{ __('Choose team members...') }}">
                @foreach($project->users as $user)
                    <option value="{{ $user->id }}" data-avatar="{{ strtoupper(substr($user->name, 0, 1)) }}">
                        {{ $user->name }} ({{ $user->email }})
                    </option>
                @endforeach
            </select>
        </div>
    @else
        <div class="alert alert-warning">
            <i class="ti ti-alert-triangle me-2"></i>
            {{ __('No users assigned to this project. Please assign users to the project first.') }}
        </div>
    @endif
    @if(isset($settings['google_calendar_enable']) && $settings['google_calendar_enable'] == 'on')
        <div class="form-group col-md-6">
            {{Form::label('synchronize_type',__('Synchronize in Google Calendar ?'),array('class'=>'form-label')) }}
            <div class="form-switch">
                <input type="checkbox" class="form-check-input mt-2" name="synchronize_type" id="switch-shadow" value="google_calender">
                <label class="form-check-label" for="switch-shadow"></label>
            </div>
        </div>
    @endif
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn btn-primary">
</div>
{{Form::close()}}

<style>
.user-select-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.user-option {
    position: relative;
}

.user-option input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.user-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.user-option input[type="checkbox"]:checked + .user-label {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.2;
}

.user-check-indicator {
    opacity: 0;
    transition: all 0.2s ease;
    color: #10b981;
    font-size: 1rem;
}

.user-option input[type="checkbox"]:checked + .user-label .user-check-indicator {
    opacity: 1;
    transform: scale(1.1);
}

.selected-users-summary {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.selected-user-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: #eff6ff;
    color: #3b82f6;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0.125rem;
}
</style>

<script>
$(document).ready(function() {
    // Initialize Select2 for task members
    $('#assign_to_members').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: '{{ __("Choose team members...") }}',
        allowClear: true,
        closeOnSelect: false,
        templateResult: formatUser,
        templateSelection: formatUserSelection
    });

    // Custom formatting for user options in dropdown
    function formatUser(user) {
        if (!user.id) {
            return user.text;
        }
        
        const avatar = $(user.element).data('avatar');
        const userInfo = user.text.split('(');
        const userName = userInfo[0].trim();
        const userEmail = userInfo[1] ? userInfo[1].replace(')', '').trim() : '';
        
        return $(`
            <div class="d-flex align-items-center">
                <div class="user-avatar-small me-2">${avatar}</div>
                <div>
                    <div class="user-name-small">${userName}</div>
                    <small class="text-muted">${userEmail}</small>
                </div>
            </div>
        `);
    }

    // Custom formatting for selected users
    function formatUserSelection(user) {
        if (!user.id) {
            return user.text;
        }
        
        const userName = user.text.split('(')[0].trim();
        return userName;
    }

    // Form validation enhancement
    $('#create_task').on('submit', function(e) {
        // Check if at least one user is selected
        const selectedUsers = $('#assign_to_members').find('option:selected').length;
        if (selectedUsers === 0) {
            e.preventDefault();
            show_toastr('error', '{{ __("Please select at least one user to assign this task.") }}');
            return false;
        }
    });

    // Initialize Select2 for tags with tag creation functionality
    $('#tags-select').select2({
        placeholder: '{{ __("Select or create tags...") }}',
        allowClear: true,
        tags: true, // Enable tag creation
        width: '100%',
        dropdownParent: $('body'), // Ensure dropdown appears above modal
        minimumResultsForSearch: 0,
        closeOnSelect: false,
        
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#tags-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        },
        
        templateSelection: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        var data = e.params.data;
        
        // If this is a new tag, create it via AJAX
        if (data.newTag) {
            $.ajax({
                url: '{{ route("tags.store") }}',
                method: 'POST',
                data: {
                    name: data.text,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Update the option with the real tag ID
                        var option = $('#tags-select option[value="' + data.id + '"]');
                        option.val(response.tag.id);
                        option.text(data.text);
                        
                        // Update the selected option
                        $('#tags-select').val(function(idx, val) {
                            var newVal = val.filter(function(v) { return v !== data.id; });
                            newVal.push(response.tag.id);
                            return newVal;
                        });
                        
                        show_toastr('Success', '{{ __("Tag created successfully!") }}', 'success');
                    } else {
                        show_toastr('Error', response.message || '{{ __("Failed to create tag") }}', 'error');
                    }
                },
                error: function() {
                    show_toastr('Error', '{{ __("Failed to create tag") }}', 'error');
                }
            });
        }
    });
});
</script>

<style>
/* Custom styles for Select2 user dropdown */
.user-avatar-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.7rem;
}

.user-name-small {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.2;
}

/* Custom Select2 styling */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: 45px;
    border-radius: 0.5rem;
    border: 2px solid #e5e7eb;
    padding: 0.25rem;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus,
.select2-container--bootstrap4.select2-container--focus .select2-selection--multiple {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select2-container--bootstrap4 .select2-results__option {
    padding: 0.5rem;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #eff6ff;
    color: #3b82f6;
}
</style>

