{{-- resources/views/custom-fields/create.blade.php --}}
{{ Form::open(['url' => route('custom-field.store'), 'class' => 'needs-validation', 'novalidate', 'id' => 'createCustomFieldForm', 'onsubmit' => 'return false;']) }}
<div class="modal-body">
    <div class="row">
        {{-- Field Name --}}
        <div class="form-group col-md-12">
            {{ Form::label('name', __('Custom Field Name'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Custom Field Name'), 'id' => 'field_name']) }}
        </div>

        {{-- Unique Key (Auto-generated) --}}
        <div class="form-group col-md-12">
    {{ Form::label('unique_key', __('Unique Key'), ['class' => 'form-label']) }} <x-required />
    {{ Form::text('unique_key', null, ['class' => 'form-control', 'readonly' => true, 'id' => 'unique_key', 'required' => true]) }}
</div>

{{-- Field Type --}}
<div class="form-group">
    {{ Form::label('type', __('Type'), ['class' => 'form-label']) }} <x-required />
    {{ Form::select('type', $types, null, ['class' => 'form-control select', 'id' => 'type']) }}
</div>

{{-- Dynamic Options Input --}}
<div class="form-group col-md-12" id="options-container" style="display:none;">
    {{ Form::label('options[]', __('Options (for dropdown/select/checkbox/multiselect)'), ['class' => 'form-label']) }}
    <small class="form-text text-muted mb-2">{{ __('Add options for your field. Each option will be available for selection.') }}</small>
    <div id="options-list">
        <div class="d-flex mb-2 option-row">
            {{ Form::text('options[]', null, ['class' => 'form-control me-2', 'placeholder' => __('Enter option value'), 'required' => false]) }}
            <button type="button" class="btn btn-sm btn-success add-option" title="{{ __('Add Option') }}">
                <i class="ti ti-plus"></i>
            </button>
        </div>
    </div>
    <div class="mt-2">
        <small class="text-info">
            <i class="ti ti-info-circle me-1"></i>
            {{ __('Click the + button to add more options. At least one option is required for these field types.') }}
        </small>
    </div>
</div>


        
        {{-- Module --}}
        <div class="form-group col-md-12">
            {{ Form::label('module', __('Module'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::select('module', $modules, null, ['class' => 'form-control select', 'required' => 'required']) }}
        </div>

        {{-- Is Required --}}
        <div class="form-group col-md-12">
            {{ Form::label('is_required', __('Is Required'), ['class' => 'form-label']) }}
            {{ Form::select('is_required', [0 => __('No'), 1 => __('Yes')], 0, ['class' => 'form-control select']) }}
        </div>

        {{-- Status --}}
        <div class="form-group col-md-12">
            {{ Form::label('status', __('Status'), ['class' => 'form-label']) }}
            {{ Form::select('status', [1 => __('Active'), 0 => __('Inactive')], 1, ['class' => 'form-control select']) }}
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="button" value="{{ __('Create') }}" class="btn btn-primary" id="create-custom-field-btn">
</div>
{{ Form::close() }}

<script>
    $(document).on('input change', 'input[name="name"], select[name="module"]', function () {
        const name = $('input[name="name"]').val().toLowerCase().trim();
        const module = $('select[name="module"]').val().toLowerCase().trim();

        const slug = function(text) {
            return text
                .replace(/\s+/g, '_')
                .replace(/[^\w\-]+/g, '')
                .replace(/\_\_+/g, '_')
                .replace(/^_+/, '')
                .replace(/_+$/, '');
        };

        if (name && module) {
            const key = '{' + '{' + slug(module) + '.' + slug(name) + '}' + '}';
            $('input[name="unique_key"]').val(key);
        } else {
            $('input[name="unique_key"]').val('');
        }
    });
</script>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        const typeSelect = document.getElementById('type');
        const optionsContainer = document.getElementById('options-container');
        const optionsList = document.getElementById('options-list');

        // Field types that require options (only these will show the dynamic options field)
        const optionFieldTypes = ['select', 'radio', 'checkbox', 'multiselect'];

        function toggleOptions() {
            const selected = typeSelect.value;
            if (optionFieldTypes.includes(selected)) {
                optionsContainer.style.display = 'block';
                // Ensure at least one option field exists
                if (optionsList.children.length === 0) {
                    addDefaultOptionInput();
                }
                // Make option fields required when visible
                updateOptionRequiredState(true);
            } else {
                optionsContainer.style.display = 'none';
                // Clear all options when not needed
                optionsList.innerHTML = '';
                // Reset to default single option field for next time
                addDefaultOptionInput();
                // Remove required state when hidden
                updateOptionRequiredState(false);
            }
        }

        function addDefaultOptionInput() {
            const inputDiv = document.createElement('div');
            inputDiv.className = 'd-flex mb-2 option-row';
            inputDiv.innerHTML = `
                <input type="text" name="options[]" class="form-control me-2" placeholder="${'{{ __("Enter option value") }}'}" required="false">
                <button type="button" class="btn btn-sm btn-success add-option" title="${'{{ __("Add Option") }}'}">
                    <i class="ti ti-plus"></i>
                </button>
            `;
            optionsList.appendChild(inputDiv);
        }

        function addOptionInput() {
            const optionCount = optionsList.children.length + 1;
            const inputDiv = document.createElement('div');
            inputDiv.className = 'd-flex mb-2 option-row';
            inputDiv.innerHTML = `
                <input type="text" name="options[]" class="form-control me-2" placeholder="${'{{ __("Enter option value") }}'} ${optionCount}" required="false">
                <button type="button" class="btn btn-sm btn-danger remove-option" title="${'{{ __("Remove Option") }}'}">
                    <i class="ti ti-minus"></i>
                </button>
            `;
            optionsList.appendChild(inputDiv);

            // Update the first option's button to add button if it was the only one
            updateButtonStates();
        }

        function updateButtonStates() {
            const optionRows = optionsList.querySelectorAll('.option-row');

            optionRows.forEach((row, index) => {
                const button = row.querySelector('button');
                if (optionRows.length === 1) {
                    // Only one option - show add button
                    button.className = 'btn btn-sm btn-success add-option';
                    button.innerHTML = '<i class="ti ti-plus"></i>';
                    button.title = '{{ __("Add Option") }}';
                } else if (index === 0) {
                    // First option - show add button
                    button.className = 'btn btn-sm btn-success add-option';
                    button.innerHTML = '<i class="ti ti-plus"></i>';
                    button.title = '{{ __("Add Option") }}';
                } else {
                    // Other options - show remove button
                    button.className = 'btn btn-sm btn-danger remove-option';
                    button.innerHTML = '<i class="ti ti-minus"></i>';
                    button.title = '{{ __("Remove Option") }}';
                }
            });
        }

        function updateOptionRequiredState(required) {
            const optionInputs = optionsList.querySelectorAll('input[name="options[]"]');
            optionInputs.forEach(input => {
                if (required) {
                    input.setAttribute('required', 'required');
                } else {
                    input.removeAttribute('required');
                }
            });
        }

        // Event delegation for dynamic buttons
        optionsList.addEventListener('click', function (e) {
            const button = e.target.closest('button');
            if (!button) return;

            if (button.classList.contains('remove-option')) {
                e.preventDefault();
                const row = button.closest('.option-row');
                row.remove();
                updateButtonStates();

                // Ensure at least one option exists for option field types
                if (optionsList.children.length === 0 && optionFieldTypes.includes(typeSelect.value)) {
                    addDefaultOptionInput();
                    updateButtonStates();
                }
            } else if (button.classList.contains('add-option')) {
                e.preventDefault();
                addOptionInput();
            }
        });

        // Initialize on type change
        typeSelect.addEventListener('change', toggleOptions);

        // Initialize on page load
        toggleOptions();
    });

    // Handle form submission via AJAX
    $(document).ready(function() {
        // Handle form submission via AJAX
        $('#createCustomFieldForm').off('submit').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var form = $(this);
            var submitBtn = $('#create-custom-field-btn');
            var originalText = submitBtn.val();
            
            submitBtn.val('Creating...').prop('disabled', true);
            
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success toast
                        showToast('success', response.message);
                        
                        // Close modal
                        $('.modal').modal('hide');
                        
                        // Refresh the custom fields table
                        if (response.custom_fields) {
                            refreshCustomFieldsTable(response.custom_fields);
                        } else {
                            // Fallback to page reload if no data provided
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        showToast('error', response.message || 'Error creating custom field');
                    }
                },
                error: function(xhr) {
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errorMessage = '';
                        $.each(xhr.responseJSON.errors, function(key, value) {
                            errorMessage += value[0] + '\n';
                        });
                        showToast('error', 'Validation errors:\n' + errorMessage);
                    } else {
                        showToast('error', 'Error creating custom field');
                    }
                },
                complete: function() {
                    submitBtn.val(originalText).prop('disabled', false);
                }
            });
            
            return false; // Prevent form submission
        });

        // Handle submit button click
        $('#create-custom-field-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var form = $('#createCustomFieldForm');
            var submitBtn = $(this);
            var originalText = submitBtn.val();
            
            // Validate form
            if (!form[0].checkValidity()) {
                form[0].reportValidity();
                return false;
            }
            
            submitBtn.val('Creating...').prop('disabled', true);
            
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: form.serialize(),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Show success toast
                        showToast('success', response.message);
                        
                        // Close modal
                        $('.modal').modal('hide');
                        
                        // Refresh the custom fields table
                        if (response.custom_fields) {
                            refreshCustomFieldsTable(response.custom_fields);
                        } else {
                            // Fallback to page reload if no data provided
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        showToast('error', response.message || 'Error creating custom field');
                    }
                },
                error: function(xhr) {
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errorMessage = '';
                        $.each(xhr.responseJSON.errors, function(key, value) {
                            errorMessage += value[0] + '\n';
                        });
                        showToast('error', 'Validation errors:\n' + errorMessage);
                    } else {
                        showToast('error', 'Error creating custom field');
                    }
                },
                complete: function() {
                    submitBtn.val(originalText).prop('disabled', false);
                }
            });
            
            return false;
        });
    });

    // Toast notification function
    function showToast(type, message) {
        // Check if toast container exists, if not create it
        if (!$('#toast-container').length) {
            $('body').append('<div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>');
        }
        
        var toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
        var icon = type === 'success' ? 'ti ti-check' : 'ti ti-alert-circle';
        
        var toast = $(`
            <div class="toast align-items-center text-white border-0 ${toastClass}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${icon} me-2"></i>${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `);
        
        $('#toast-container').append(toast);
        
        // Initialize and show toast
        var bsToast = new bootstrap.Toast(toast[0]);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }

    // Function to refresh custom fields table
    function refreshCustomFieldsTable(customFields) {
        var tbody = $('table tbody');
        tbody.empty();
        
        if (customFields.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <div class="text-muted">
                            <i class="ti ti-inbox fs-1 mb-3 d-block"></i>
                            <h5>{{ __('No custom fields found') }}</h5>
                            <p>{{ __('Create your first custom field to get started') }}</p>
                        </div>
                    </td>
                </tr>
            `);
            return;
        }
        
        customFields.forEach(function(field) {
            var requiredBadge = field.is_required ? 
                '<div class="chip-chip bg-success-subtle text-success"><i class="ti ti-check me-1"></i><span class="chip-text">{{ __("Yes") }}</span></div>' :
                '<div class="chip-chip bg-secondary-subtle text-secondary"><i class="ti ti-minus me-1"></i><span class="chip-text">{{ __("No") }}</span></div>';
            
            var statusToggle = field.status ? 'checked' : '';
            var statusLabel = field.status ? '{{ __("Active") }}' : '{{ __("Inactive") }}';
            
            var row = `
                <tr class="border-bottom">
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="form-check">
                            <input class="form-check-input row-checkbox" type="checkbox" 
                                   value="${field.id}" 
                                   id="field-${field.id}">
                        </div>
                    </td>
                    <td class="py-3 px-4 align-middle">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary-subtle rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="ti ti-forms text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-medium">${field.name}</h6>
                            </div>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="chip-chip bg-primary-subtle text-primary">
                            <i class="ti ti-type me-1"></i>
                            <span class="chip-text">${field.type}</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="chip-chip bg-info-subtle text-info">
                            <i class="ti ti-layers me-1"></i>
                            <span class="chip-text">${field.module}</span>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        ${requiredBadge}
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="form-check form-switch d-flex justify-content-center">
                            <input class="form-check-input status-toggle" type="checkbox" 
                                   id="status-${field.id}" 
                                   data-field-id="${field.id}"
                                   ${statusToggle}
                                   style="width: 3rem; height: 1.5rem;">
                            <label class="form-check-label ms-2 fw-medium" for="status-${field.id}">
                                ${statusLabel}
                            </label>
                        </div>
                    </td>
                    <td class="py-3 px-4 text-center align-middle">
                        <div class="d-flex justify-content-center gap-2">
                            <a href="#" class="btn btn-outline-primary btn-sm px-3" 
                               data-url="{{ route('custom-field.edit', '') }}/${field.id}" 
                               data-ajax-popup="true" 
                               data-title="{{ __('Edit Custom Field') }}"
                               title="{{ __('Edit') }}">
                                <i class="ti ti-pencil me-1"></i>{{ __('Edit') }}
                            </a>
                            <form method="DELETE" action="{{ route('custom-field.destroy', '') }}/${field.id}" style="display:inline">
                                <button type="submit" class="btn btn-outline-danger btn-sm px-3 delete-field-btn" 
                                        data-field-id="${field.id}"
                                        title="{{ __('Delete') }}">
                                    <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Reinitialize any necessary event handlers
        initializeTableHandlers();
    }

    // Function to initialize table event handlers
    function initializeTableHandlers() {
        // Reinitialize status toggle handlers
        $('.status-toggle').off('change').on('change', function() {
            var fieldId = $(this).data('field-id');
            var status = $(this).is(':checked');
            toggleFieldStatus(fieldId, status);
        });
        
        // Reinitialize checkbox handlers
        $('.row-checkbox').off('change').on('change', function() {
            updateSelectAllState();
            updateBulkActionsVisibility();
        });
        
        // Reinitialize delete handlers
        $('.delete-field-btn').off('click').on('click', function(e) {
            e.preventDefault();
            var fieldId = $(this).data('field-id');
            if (confirm('{{ __("Are you sure you want to delete this custom field?") }}')) {
                deleteField(fieldId);
            }
        });
    }

    // Helper functions for table operations
    function toggleFieldStatus(fieldId, status) {
        fetch('{{ route("custom-field.toggle-status") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                field_id: fieldId,
                status: status
            })
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
            } else {
                showToast('error', data.message || 'Error updating status');
            }
        }).catch(error => {
            showToast('error', 'Error updating status');
        });
    }

    function updateSelectAllState() {
        var totalCheckboxes = $('.row-checkbox').length;
        var checkedCheckboxes = $('.row-checkbox:checked').length;
        var selectAllCheckbox = $('#select-all');
        
        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false).prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    }

    function updateBulkActionsVisibility() {
        var checkedCount = $('.row-checkbox:checked').length;
        var bulkActionsBar = $('#bulk-actions-bar');
        
        if (checkedCount > 0) {
            bulkActionsBar.show();
            $('#selected-count').text(checkedCount);
        } else {
            bulkActionsBar.hide();
        }
    }

    function deleteField(fieldId) {
        fetch(`/custom-field/${fieldId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('success', data.message);
                // Refresh the table
                refreshCustomFieldsTable(data.custom_fields || []);
            } else {
                showToast('error', data.message || 'Error deleting field');
            }
        }).catch(error => {
            showToast('error', 'Error deleting field');
        });
    }
</script>
