{{ Form::open(['url' => route('tags.store'), 'class' => 'needs-validation', 'novalidate']) }}
<div class="modal-body">
    <div class="row">
        {{-- Tag Name --}}
        <div class="form-group col-md-12">
            {{ Form::label('name', __('Tag Name'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Tag Name'), 'id' => 'tag_name']) }}
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="text-end">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
        {{ Form::submit(__('Create Tag'), ['class' => 'btn btn-primary']) }}
    </div>
</div>
{{ Form::close() }} 