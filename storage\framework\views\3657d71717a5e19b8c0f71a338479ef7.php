<?php if($customFields): ?>
    <?php $__currentLoopData = $customFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customField): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php
            $fieldName = 'custom_field[' . $customField->id . ']';
            $label = Form::label('custom_field-'.$customField->id, __($customField->name), ['class' => 'form-label']);
            $fieldValue = isset($customFieldValues[$customField->id]) ? $customFieldValues[$customField->id] : null;
            $isRequired = $customField->is_required;
        ?>

        <div class="col-lg-4 col-md-4 col-sm-6 col-12">
            <div class="form-group">
                <?php echo $label; ?>

                <?php if($isRequired): ?>
                    <?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
                <?php endif; ?>
                <div class="input-group">
                    
                    <?php switch($customField->type):

                        case ('text'): ?>
                            <?php echo e(Form::text($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('email'): ?>
                            <?php echo e(Form::email($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('number'): ?>
                            <?php echo e(Form::number($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('date'): ?>
                            <?php echo e(Form::date($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('datetime'): ?>
                            <?php echo e(Form::datetimeLocal($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('textarea'): ?>
                            <?php echo e(Form::textarea($fieldName, $fieldValue, ['class' => 'form-control', 'rows' => 1, 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                            
                            <?php case ('checkbox'): ?>
    <?php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); ?>
    <?php $selectedValues = is_array($fieldValue) ? $fieldValue : (is_string($fieldValue) ? json_decode($fieldValue, true) : []); ?>
    <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="form-check">
            <?php echo e(Form::checkbox($fieldName.'[]', $option, in_array($option, $selectedValues), ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index, 'required' => $isRequired ? 'required' : null])); ?>

            <?php echo e(Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label'])); ?>

        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php break; ?>

<?php case ('radio'): ?>
    <?php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); ?>
    <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="form-check">
            <?php echo e(Form::radio($fieldName, $option, $fieldValue == $option, ['class' => 'form-check-input', 'id' => $fieldName.'_'.$loop->index, 'required' => $isRequired ? 'required' : null])); ?>

            <?php echo e(Form::label($fieldName.'_'.$loop->index, $option, ['class' => 'form-check-label'])); ?>

        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php break; ?>

<?php case ('select'): ?>
    <?php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); ?>
    <?php $selectOptions = ['' => 'Select an option']; ?>
    <?php if($options): ?>
        <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php $selectOptions[$option] = $option; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
    <?php echo e(Form::select($fieldName, $selectOptions, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

    <?php break; ?>

<?php case ('multiselect'): ?>
    <?php $options = is_array($customField->options) ? $customField->options : json_decode($customField->options, true); ?>
    <?php $selectOptions = []; ?>
    <?php $selectedValues = is_array($fieldValue) ? $fieldValue : (is_string($fieldValue) ? json_decode($fieldValue, true) : []); ?>
    <?php if($options): ?>
        <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php $selectOptions[$option] = $option; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
    <?php echo e(Form::select($fieldName.'[]', $selectOptions, $selectedValues, ['class' => 'form-control', 'multiple' => 'multiple', 'size' => '4', 'required' => $isRequired ? 'required' : null])); ?>

    <small class="form-text text-muted"><?php echo e(__('Hold Ctrl (Cmd on Mac) to select multiple options')); ?></small>
    <?php break; ?>

                        <?php case ('link'): ?>
                            <?php echo e(Form::url($fieldName, $fieldValue, ['class' => 'form-control', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('color'): ?>
                             <?php echo e(Form::input('color', $fieldName, $fieldValue ?: '#000000', ['class' => 'form-control form-control-color', 'title' => 'Pick a color', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php break; ?>

                        <?php case ('file'): ?>
                            <?php echo e(Form::file($fieldName, ['class' => 'form-control', 'accept' => '*/*', 'required' => $isRequired ? 'required' : null])); ?>

                            <?php if($fieldValue): ?>
                                <small class="form-text text-muted"><?php echo e(__('Current file:')); ?> <?php echo e($fieldValue); ?></small>
                            <?php endif; ?>
                            <?php break; ?>

                        <?php case ('file_multiple'): ?>
                            <?php echo e(Form::file($fieldName.'[]', ['class' => 'form-control', 'multiple' => 'multiple', 'accept' => '*/*', 'required' => $isRequired ? 'required' : null])); ?>

                            <small class="form-text text-muted"><?php echo e(__('You can select multiple files')); ?></small>
                            <?php if($fieldValue): ?>
                                <small class="form-text text-muted"><?php echo e(__('Current files:')); ?> <?php echo e(is_array($fieldValue) ? implode(', ', $fieldValue) : $fieldValue); ?></small>
                            <?php endif; ?>
                            <?php break; ?>

                        <?php default: ?>
                            <span class="text-danger"><?php echo e(__('Unknown Field Type')); ?></span>
                    <?php endswitch; ?>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/customFields/formBuilder.blade.php ENDPATH**/ ?>