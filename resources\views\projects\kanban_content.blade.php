@php
    // Get stages for the current user/creator
    $stages = \App\Models\TaskStage::where('created_by', \Auth::user()->creatorId())->orderBy('order')->get();

    // If no stages exist, create default ones
    if ($stages->count() == 0) {
        $defaultStages = ['To Do', 'In Progress', 'Review', 'Done'];
        foreach ($defaultStages as $key => $stageName) {
            \App\Models\TaskStage::create([
                'name' => $stageName,
                'order' => $key,
                'created_by' => \Auth::user()->creatorId(),
            ]);
        }
        // Re-fetch stages after creating defaults
        $stages = \App\Models\TaskStage::where('created_by', \Auth::user()->creatorId())->orderBy('order')->get();
    }

    // Get all tasks for this project
    $allTasks = \App\Models\Project::projectTask($project->id);

    // Add tasks to each stage
    foreach ($stages as $stage) {
        $stage->tasks = $allTasks->where('stage_id', $stage->id);
    }
@endphp

<!-- Add Stage Button -->
@can('create project task stage')
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">{{ __('Project Stages') }}</h6>
                <div class="d-flex gap-2">
                    @if(config('app.debug'))
                        <button onclick="testDragula()" 
                                class="btn btn-sm btn-outline-info"
                                title="{{ __('Test Drag & Drop') }}">
                            <i class="ti ti-bug"></i> {{ __('Test') }}
                        </button>
                    @endif
                    <button onclick="manualRefresh()" 
                            class="btn btn-sm btn-outline-secondary"
                            title="{{ __('Refresh Kanban Board') }}">
                        <i class="ti ti-refresh"></i> {{ __('Refresh') }}
                    </button>
                    <a href="#" data-url="{{ route('project-task-stages.create') }}" 
                       data-ajax-popup="true" 
                       data-size="lg"
                       data-bs-toggle="tooltip"
                       title="{{ __('Add New Stage') }}"
                       class="btn btn-sm btn-primary">
                        <i class="ti ti-plus"></i> {{ __('Add Stage') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
@endcan

@if($stages->count() > 0)
    <div class="kanban-container">
        <div class="kanban-wrapper" data-containers='{{ json_encode($stages->pluck('id')->map(function($id) { return 'task-list-' . $id; })->toArray()) }}' data-plugin="dragula">
            @foreach($stages as $stage)
                <div class="kanban-column">
                    <div class="kanban-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">{{ $stage->name }}</h6>
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-light text-dark">{{ $stage->tasks->count() }}</span>
                                @can('create project task')
                                    <a href="#" data-size="lg"
                                       data-url="{{ route('projects.tasks.create', [$project->id, $stage->id]) }}"
                                       data-ajax-popup="true" data-bs-toggle="tooltip"
                                       title="{{ __('Add Task to') }} {{ $stage->name }}"
                                       class="btn btn-sm btn-light-primary">
                                        <i class="ti ti-plus"></i>
                                    </a>
                                @endcan
                                @can('delete project task stage')
                                    <button type="button" class="btn btn-danger btn-sm" 
                                            onclick="showDeleteConfirmation({{ $stage->id }}, '{{ $stage->name }}')"
                                            title="{{ __('Delete Stage') }}">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                @endcan
                                @can('edit project task stage')
                                    <a href="#" 
                                       data-url="{{ route('project-task-stages.edit', $stage->id) }}"
                                       data-ajax-popup="true" 
                                       data-size="lg"
                                       data-bs-toggle="tooltip"
                                       title="{{ __('Edit Stage') }} {{ $stage->name }}"
                                       class="btn btn-sm btn-light-warning">
                                        <i class="ti ti-edit"></i>
                                    </a>
                                @endcan
                            </div>
                        </div>
                    </div>
                    <div class="kanban-tasks task-list-{{ $stage->id }}" data-stage-id="{{ $stage->id }}">
                        @foreach($stage->tasks as $task)
                            <div class="kanban-task-card task-card-draggable task-clickable"
                                 data-task-id="{{ $task->id }}"
                                 data-stage-id="{{ $task->stage_id }}"
                                 data-project-id="{{ $project->id }}"
                                 data-task-url="{{ route('projects.tasks.show', [$project->id, $task->id]) }}"
                                 style="cursor: grab;">

                                <!-- Task Header -->
                                <div class="task-header">
                                    <h6 class="task-title">
                                        {{ $task->name }}
                                    </h6>
                                    <span class="task-priority-badge priority-{{ $task->priority }}">
                                        {{ $task->priority }}
                                    </span>
                                </div>

                                <!-- Task Description -->
                                @if($task->description)
                                    <p class="task-description">{{ Str::limit($task->description, 80) }}</p>
                                @endif

                                <!-- Task Footer -->
                                <div class="task-footer">
                                    <div class="task-assignees">
                                        @if($task->assign_to)
                                            @foreach(explode(',', $task->assign_to) as $userId)
                                                @php $user = \App\Models\User::find($userId); @endphp
                                                @if($user)
                                                    <div class="task-avatar" title="{{ $user->name }}">
                                                        {{ substr($user->name, 0, 1) }}
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="task-date">
                                        {{ \Carbon\Carbon::parse($task->end_date)->format('M d') }}
                                    </div>
                                </div>

                                <!-- Task Stats -->
                                @php
                                    $taskComments = $task->comments ?? collect();
                                    $taskFiles = $task->taskFiles ?? collect();
                                @endphp
                                @if($taskComments->count() > 0 || $taskFiles->count() > 0)
                                    <div class="task-stats">
                                        @if($taskComments->count() > 0)
                                            <div class="task-stat">
                                                <i class="ti ti-message-circle"></i>
                                                <span>{{ $taskComments->count() }}</span>
                                            </div>
                                        @endif
                                        @if($taskFiles->count() > 0)
                                            <div class="task-stat">
                                                <i class="ti ti-paperclip"></i>
                                                <span>{{ $taskFiles->count() }}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endif


                            </div>
                        @endforeach

                        @if($stage->tasks->count() == 0)
                            <div class="empty-stage">
                                <div class="empty-stage-icon">
                                    <i class="ti ti-plus"></i>
                                </div>
                                <p class="empty-stage-text">{{ __('Drop tasks here') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@else
    <div class="text-center py-5">
        <i class="ti ti-layout-kanban" style="font-size: 3rem; color: #ccc;"></i>
        <h5 class="mt-3 text-muted">{{ __('No Task Stages Found') }}</h5>
        <p class="text-muted">{{ __('Task stages should have been created automatically.') }}</p>
        <p class="text-muted small">
            {{ __('Debug Info:') }}<br>
            {{ __('Stages found: ') . $stages->count() }}<br>
            {{ __('Tasks found: ') . $allTasks->count() }}<br>
            {{ __('Creator ID: ') . \Auth::user()->creatorId() }}
        </p>
    </div>
@endif



<script>
// Function to refresh the Kanban board after stage creation
function refreshKanbanBoard() {
    // Prevent multiple simultaneous refreshes
    if (window.isRefreshing) {
        console.log('Refresh already in progress, skipping...');
        return;
    }
    
    window.isRefreshing = true;
    console.log('Starting Kanban board refresh...');
    
    // Clear all loading states before refreshing
    clearAllLoadingStates();
    
    $.ajax({
        url: '{{ route("projects.show", $project->id) }}',
        method: 'GET',
        data: { refresh_kanban: true },
        success: function(response) {
            console.log('Kanban refresh successful');
            // The response should be the HTML content directly
            $('#task-kanban').html(response);
            // Reinitialize dragula after a short delay to ensure DOM is ready
            setTimeout(function() {
                initializeDragula();
                updateStageCounters();
                reinitializeEventHandlers();
                window.isRefreshing = false; // Reset flag
                console.log('Kanban board refresh completed');
            }, 100);
            show_toastr('Success', '{{ __("Kanban board refreshed successfully") }}', 'success');
        },
        error: function() {
            window.isRefreshing = false; // Reset flag on error
            console.log('Kanban refresh failed');
            show_toastr('Error', '{{ __("Failed to refresh Kanban board") }}', 'error');
        }
    });
}

// Smart refresh function that only refreshes if needed
function smartRefresh(reason, delay = 200) {
    console.log('Smart refresh requested for:', reason);
    
    // Cancel any pending refresh
    if (window.refreshTimeout) {
        clearTimeout(window.refreshTimeout);
    }
    
    // Schedule refresh with delay to allow for multiple rapid operations
    window.refreshTimeout = setTimeout(function() {
        console.log('Executing smart refresh for:', reason);
        refreshKanbanBoard();
    }, delay);
}

// Function to reset and reinitialize stage handlers
function resetStageHandlers() {
    console.log('Resetting stage handlers...');
    window.stageHandlersInitialized = false;
    $(document).off('submit.stageEdit');
    $(document).off('submit.stageCreate');
    initializeStageFormHandlers();
}

// Function to reinitialize event handlers after refresh
function reinitializeEventHandlers() {
    // Reinitialize tooltips
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Ensure AJAX popup handlers are reattached
    if (typeof ajaxPopup !== 'undefined') {
        $('[data-ajax-popup="true"]').off('click').on('click', function(e) {
            e.preventDefault();
            ajaxPopup($(this));
        });
    }
    
    // Initialize task card click handlers
    initializeTaskCardHandlers();
    
    // Reset and reinitialize stage form handlers
    resetStageHandlers();
    
    // Setup AJAX defaults
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    });
}

// Function to initialize task card click and drag handlers
function initializeTaskCardHandlers() {
    // Remove existing handlers first
    $(document).off('mousedown.taskCard', '.task-clickable');
    $(document).off('mouseup.taskCard', '.task-clickable');
    $(document).off('mousemove.taskCard', '.task-clickable');
    
    let isDragging = false;
    let startX, startY;
    let dragThreshold = 5; // pixels
    
    // Handle mouse down to detect start of potential drag
    $(document).on('mousedown.taskCard', '.task-clickable', function(e) {
        // Only handle left mouse button
        if (e.which !== 1) return;
        
        isDragging = false;
        startX = e.pageX;
        startY = e.pageY;
        
        // Change cursor to grabbing when mouse is down
        $(this).css('cursor', 'grabbing');
    });
    
    // Handle mouse move to detect if user is dragging
    $(document).on('mousemove.taskCard', '.task-clickable', function(e) {
        if (e.which === 1 && (startX !== undefined && startY !== undefined)) {
            const deltaX = Math.abs(e.pageX - startX);
            const deltaY = Math.abs(e.pageY - startY);
            
            if (deltaX > dragThreshold || deltaY > dragThreshold) {
                isDragging = true;
                $(this).css('cursor', 'grabbing');
            }
        }
    });
    
    // Handle mouse up to determine if it's a click or end of drag
    $(document).on('mouseup.taskCard', '.task-clickable', function(e) {
        // Only handle left mouse button
        if (e.which !== 1) return;
        
        const $taskCard = $(this);
        
        // Reset cursor
        $taskCard.css('cursor', 'grab');
        
        // If it wasn't a drag, treat it as a click
        if (!isDragging) {
            e.preventDefault();
            e.stopPropagation();
            
            const taskUrl = $taskCard.data('task-url');
            console.log('Task card clicked:', {
                taskId: $taskCard.data('task-id'),
                taskUrl: taskUrl
            });
            
            if (taskUrl) {
                // Use direct AJAX call to open task details modal
                console.log('Opening task details via AJAX...');
                $.ajax({
                    url: taskUrl,
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        console.log('Task details loaded successfully');
                        // Set modal title and content
                        $('#commonModal .modal-title').html('{{ __("Task Details") }}');
                        $('#commonModal .body').html(response);
                        // Show the modal
                        $('#commonModal').modal('show');
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to load task details:', {
                            status: xhr.status,
                            error: error,
                            response: xhr.responseText
                        });
                        show_toastr('Error', '{{ __("Failed to load task details") }}', 'error');
                    }
                });
            } else {
                console.error('No task URL found on card');
                show_toastr('Error', '{{ __("Task URL not found") }}', 'error');
            }
        }
        
        // Reset tracking variables
        isDragging = false;
        startX = undefined;
        startY = undefined;
    });
    
    // Handle mouse leave to reset cursor
    $(document).on('mouseleave.taskCard', '.task-clickable', function() {
        $(this).css('cursor', 'grab');
        // Reset tracking variables if mouse leaves
        isDragging = false;
        startX = undefined;
        startY = undefined;
    });
}

// Global form submission blocker
$(document).on('submit', 'form', function(e) {
    var form = $(this);
    
    // Only apply to our stage forms
    if (form.attr('action') && (form.attr('action').includes('project-task-stages') || form.attr('action').includes('project-task-new-stage'))) {
        var currentTime = new Date().getTime();
        var lastSubmit = form.data('last-submit-time') || 0;
        
        // Prevent rapid successive submissions (within 1 second)
        if (currentTime - lastSubmit < 1000) {
            console.log('Rapid form submission blocked:', {
                action: form.attr('action'),
                timeSinceLastSubmit: currentTime - lastSubmit
            });
            e.preventDefault();
            e.stopImmediatePropagation();
            return false;
        }
        
        // Update last submit time
        form.data('last-submit-time', currentTime);
    }
});

// Function to initialize stage form handlers
function initializeStageFormHandlers() {
    // Prevent multiple initializations
    if (window.stageHandlersInitialized) {
        console.log('Stage handlers already initialized, skipping...');
        return;
    }
    
    // Remove any existing handlers first - be more specific with namespaces
    $(document).off('submit.stageEdit');
    $(document).off('submit.stageCreate');
    
    console.log('Initializing stage form handlers...');
    
    // Mark as initialized
    window.stageHandlersInitialized = true;
    
    // Stage editing handler with unique namespace - use more specific selector
    $(document).on('submit.stageEdit', 'form[action*="project-task-stages"][method="post"]', function(e) {
        var form = $(this);
        var method = form.find('input[name="_method"]').val();
        
        // Only handle PUT/PATCH requests (updates)
        if (method === 'PUT' || method === 'PATCH') {
            e.preventDefault();
            e.stopImmediatePropagation(); // Prevent other handlers from firing
            
            // Get stage ID from form action URL
            var actionUrl = form.attr('action');
            var stageId = actionUrl.split('/').pop();
            var operationKey = 'stage-edit-' + stageId + '-' + new Date().getTime();
            
            console.log('Stage edit intercepted:', {
                formId: form.attr('id') || 'no-id',
                action: actionUrl,
                stageId: stageId,
                operationKey: operationKey
            });
            
            // Check if this exact form is already being submitted
            if (form.data('ajax-submitting')) {
                console.log('Form already being submitted via AJAX, blocking duplicate:', operationKey);
                return false;
            }
            
            // Mark this form as being submitted
            form.data('ajax-submitting', true);
            
            // Prevent duplicate calls for the same stage
            if (window.pendingStageUpdates && window.pendingStageUpdates[stageId]) {
                console.log('Duplicate stage edit prevented for stage:', stageId);
                form.data('ajax-submitting', false); // Reset flag
                return false;
            }
            
            // Initialize pending updates tracker
            if (!window.pendingStageUpdates) {
                window.pendingStageUpdates = {};
            }
            
            // Mark this stage as being updated
            window.pendingStageUpdates[stageId] = operationKey;
            
            // Prevent multiple form submissions
            if (form.data('submitting') || form.hasClass('submitting')) {
                console.log('Form already submitting, skipping...', operationKey);
                form.data('ajax-submitting', false); // Reset flag
                return false;
            }
            form.data('submitting', true);
            form.addClass('submitting');
            
            // Disable submit button to prevent clicking
            form.find('input[type="submit"], button[type="submit"]').prop('disabled', true);
            
            var formData = form.serialize();
            
            // Add debug info
            console.log('Stage edit AJAX starting:', {
                url: form.attr('action'),
                method: method,
                data: formData,
                operationKey: operationKey,
                stageId: stageId,
                timestamp: new Date().getTime()
            });
            
            $.ajax({
                url: form.attr('action'),
                method: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    console.log('Stage edit success:', response, operationKey);
                    
                    // Clear all pending flags
                    delete window.pendingStageUpdates[stageId];
                    form.data('submitting', false);
                    form.data('ajax-submitting', false);
                    form.removeClass('submitting');
                    form.find('input[type="submit"], button[type="submit"]').prop('disabled', false);
                    
                    if (response.success) {
                        // Close the modal
                        $('.modal').modal('hide');
                        // Clear modal backdrop
                        $('.modal-backdrop').remove();
                        $('body').removeClass('modal-open');
                        
                        // Use smart refresh to avoid conflicts
                        smartRefresh('stage-edit', 300);
                        show_toastr('Success', response.message || '{{ __("Stage updated successfully") }}', 'success');
                    } else {
                        show_toastr('Error', response.message || '{{ __("Failed to update stage") }}', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Stage edit error:', {
                        status: xhr.status,
                        response: xhr.responseJSON,
                        error: error,
                        operationKey: operationKey
                    });
                    
                    // Clear all pending flags
                    delete window.pendingStageUpdates[stageId];
                    form.data('submitting', false);
                    form.data('ajax-submitting', false);
                    form.removeClass('submitting');
                    form.find('input[type="submit"], button[type="submit"]').prop('disabled', false);
                    
                    var errorMessage = '{{ __("Failed to update stage") }}';
                    
                    // Try to get more specific error message
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    } else if (xhr.status === 403) {
                        errorMessage = '{{ __("Permission denied. You may not have the required permissions to edit stages.") }}';
                    } else if (xhr.status === 419) {
                        errorMessage = '{{ __("Session expired. Please refresh the page and try again.") }}';
                    } else if (xhr.status === 422) {
                        errorMessage = '{{ __("Invalid data. Please check your input and try again.") }}';
                    } else if (xhr.status === 500) {
                        errorMessage = '{{ __("Server error. Please try again later.") }}';
                    }
                    
                    // If permission denied, suggest refresh
                    if (xhr.status === 403 || (xhr.responseJSON && xhr.responseJSON.message && xhr.responseJSON.message.includes('Permission'))) {
                        errorMessage += ' {{ __("Try refreshing the page to reload your permissions.") }}';
                        
                        // Add refresh button to the error message
                        setTimeout(function() {
                            if (confirm('{{ __("Permission denied. Would you like to refresh the page to reload your permissions?") }}')) {
                                location.reload();
                            }
                        }, 1000);
                    }
                    
                    show_toastr('Error', errorMessage, 'error');
                }
            });
        }
    });
    
    // Stage creation handler with unique namespace
    $(document).on('submit.stageCreate', 'form[action*="project-task-new-stage"]', function(e) {
        e.preventDefault();
        var form = $(this);
        
        var operationKey = 'stage-create-' + new Date().getTime();
        
        // Prevent duplicate calls for stage creation
        if (window.pendingStageCreation) {
            console.log('Duplicate stage creation prevented:', operationKey);
            return false;
        }
        
        // Mark stage creation as pending
        window.pendingStageCreation = operationKey;
        
        // Prevent multiple form submissions
        if (form.data('submitting') || form.hasClass('submitting')) {
            console.log('Form already submitting, skipping...', operationKey);
            return false;
        }
        form.data('submitting', true);
        form.addClass('submitting');
        
        var formData = form.serialize();
        
        // Add debug info
        console.log('Stage create form submitted:', {
            url: form.attr('action'),
            data: formData,
            operationKey: operationKey,
            timestamp: new Date().getTime()
        });
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Stage create response:', response, operationKey);
                
                // Clear pending operation
                window.pendingStageCreation = null;
                form.data('submitting', false);
                form.removeClass('submitting');
                
                if (response.success) {
                    // Close the modal
                    $('.modal').modal('hide');
                    // Clear modal backdrop
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // Use smart refresh to avoid conflicts
                    smartRefresh('stage-create', 300);
                    show_toastr('Success', response.message || '{{ __("Stage created successfully") }}', 'success');
                } else {
                    show_toastr('Error', response.message || '{{ __("Failed to create stage") }}', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('Stage create error:', {
                    status: xhr.status,
                    response: xhr.responseJSON,
                    error: error,
                    operationKey: operationKey
                });
                
                // Clear pending operation
                window.pendingStageCreation = null;
                form.data('submitting', false);
                form.removeClass('submitting');
                
                var errorMessage = '{{ __("Failed to create stage") }}';
                
                // Try to get more specific error message
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.status === 403) {
                    errorMessage = '{{ __("Permission denied. You may not have the required permissions to create stages.") }}';
                } else if (xhr.status === 419) {
                    errorMessage = '{{ __("Session expired. Please refresh the page and try again.") }}';
                } else if (xhr.status === 422) {
                    errorMessage = '{{ __("Invalid data. Please check your input and try again.") }}';
                } else if (xhr.status === 500) {
                    errorMessage = '{{ __("Server error. Please try again later.") }}';
                }
                
                show_toastr('Error', errorMessage, 'error');
            }
        });
    });
}

// Ensure CSRF token is available
function ensureCSRFToken() {
    if (!$('meta[name="csrf-token"]').length) {
        $('head').append('<meta name="csrf-token" content="{{ csrf_token() }}">');
    }
}

// Listen for successful stage creation
$(document).on('stage.created', function() {
    refreshKanbanBoard();
});

// Override the default form submission for stage creation to trigger refresh
$(document).off('submit', 'form[action*="project-task-new-stage"]').on('submit', 'form[action*="project-task-new-stage"]', function(e) {
    e.preventDefault();
    var form = $(this);
    
    // Prevent multiple form submissions
    if (form.data('submitting')) {
        return false;
    }
    form.data('submitting', true);
    
    var formData = form.serialize();
    
    // Add debug info
    console.log('Stage create form submitted:', {
        url: form.attr('action'),
        data: formData
    });
    
    $.ajax({
        url: form.attr('action'),
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            console.log('Stage create response:', response);
            form.data('submitting', false); // Reset flag
            if (response.success) {
                // Close the modal
                $('.modal').modal('hide');
                // Clear modal backdrop
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                
                // Trigger refresh after a short delay
                setTimeout(function() {
                    refreshKanbanBoard();
                }, 300);
                show_toastr('Success', response.message || '{{ __("Stage created successfully") }}', 'success');
            } else {
                show_toastr('Error', response.message || '{{ __("Failed to create stage") }}', 'error');
            }
        },
        error: function(xhr, status, error) {
            form.data('submitting', false); // Reset flag
            console.log('Stage create error:', {
                status: xhr.status,
                response: xhr.responseJSON,
                error: error
            });
            
            var errorMessage = '{{ __("Failed to create stage") }}';
            
            // Try to get more specific error message
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            } else if (xhr.status === 403) {
                errorMessage = '{{ __("Permission denied. You may not have the required permissions to create stages.") }}';
            } else if (xhr.status === 419) {
                errorMessage = '{{ __("Session expired. Please refresh the page and try again.") }}';
            } else if (xhr.status === 422) {
                errorMessage = '{{ __("Invalid data. Please check your input and try again.") }}';
            } else if (xhr.status === 500) {
                errorMessage = '{{ __("Server error. Please try again later.") }}';
            }
            
            show_toastr('Error', errorMessage, 'error');
        }
    });
});

// Override the default form submission for stage editing to trigger refresh
$(document).off('submit', 'form[action*="project-task-stages"]').on('submit', 'form[action*="project-task-stages"]', function(e) {
    var form = $(this);
    var method = form.find('input[name="_method"]').val();
    
    // Only handle PUT/PATCH requests (updates)
    if (method === 'PUT' || method === 'PATCH') {
        e.preventDefault();
        e.stopImmediatePropagation(); // Prevent other handlers from firing
        
        // Prevent multiple form submissions
        if (form.data('submitting') || form.hasClass('submitting')) {
            console.log('Form already submitting, skipping...');
            return false;
        }
        form.data('submitting', true);
        form.addClass('submitting');
        
        var formData = form.serialize();
        
        // Add debug info
        console.log('Stage edit form submitted:', {
            url: form.attr('action'),
            method: method,
            data: formData,
            timestamp: new Date().getTime()
        });
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Stage edit response:', response);
                form.data('submitting', false); // Reset flag
                form.removeClass('submitting');
                if (response.success) {
                    // Close the modal
                    $('.modal').modal('hide');
                    // Clear modal backdrop
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                    
                    // Trigger refresh after a short delay
                    setTimeout(function() {
                        refreshKanbanBoard();
                    }, 300);
                    show_toastr('Success', response.message || '{{ __("Stage updated successfully") }}', 'success');
                } else {
                    show_toastr('Error', response.message || '{{ __("Failed to update stage") }}', 'error');
                }
            },
            error: function(xhr, status, error) {
                form.data('submitting', false); // Reset flag
                form.removeClass('submitting');
                console.log('Stage edit error:', {
                    status: xhr.status,
                    response: xhr.responseJSON,
                    error: error
                });
                
                var errorMessage = '{{ __("Failed to update stage") }}';
                
                // Try to get more specific error message
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.status === 403) {
                    errorMessage = '{{ __("Permission denied. You may not have the required permissions to edit stages.") }}';
                } else if (xhr.status === 419) {
                    errorMessage = '{{ __("Session expired. Please refresh the page and try again.") }}';
                } else if (xhr.status === 422) {
                    errorMessage = '{{ __("Invalid data. Please check your input and try again.") }}';
                } else if (xhr.status === 500) {
                    errorMessage = '{{ __("Server error. Please try again later.") }}';
                }
                
                // If permission denied, suggest refresh
                if (xhr.status === 403 || (xhr.responseJSON && xhr.responseJSON.message && xhr.responseJSON.message.includes('Permission'))) {
                    errorMessage += ' {{ __("Try refreshing the page to reload your permissions.") }}';
                    
                    // Add refresh button to the error message
                    setTimeout(function() {
                        if (confirm('{{ __("Permission denied. Would you like to refresh the page to reload your permissions?") }}')) {
                            location.reload();
                        }
                    }, 1000);
                }
                
                show_toastr('Error', errorMessage, 'error');
            }
        });
    }
});

// Function to delete a stage
function deleteStage(stageId, stageName) {
    console.log('Deleting stage:', { stageId, stageName });
    
    $.ajax({
        url: '{{ url("project-task-stages") }}/' + stageId,
        method: 'DELETE',
        data: {
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            console.log('Stage delete response:', response);
            // Use smart refresh to avoid conflicts
            smartRefresh('stage-delete', 200);
            show_toastr('Success', '{{ __("Stage deleted successfully") }}', 'success');
        },
        error: function(xhr) {
            console.log('Stage delete error:', xhr);
            show_toastr('Error', '{{ __("Failed to delete stage") }}', 'error');
        }
    });
}

// Function to show a confirmation modal for stage deletion
function showDeleteConfirmation(stageId, stageName) {
    // Check if stage has tasks by counting tasks in the stage column
    var stageColumn = $('.kanban-column[data-stage-id="' + stageId + '"]');
    var taskCount = stageColumn.find('.task-card').length;
    
    if (taskCount > 0) {
        // Show error message if stage has tasks - only show error, no deletion
        show_toastr('Error', '{{ __("Cannot delete stage") }} "' + stageName + '" {{ __("because it contains") }} ' + taskCount + ' {{ __("task(s)") }}. {{ __("Please move or delete all tasks from this stage first.") }}', 'error');
    } else {
        // Show confirmation dialog for empty stage
        const swalWithBootstrapButtons = Swal.mixin({
            customClass: {
                confirmButton: 'btn btn-success',
                cancelButton: 'btn btn-danger'
            },
            buttonsStyling: false
        });
        
        swalWithBootstrapButtons.fire({
            title: '{{ __("Are you sure?") }}',
            text: '{{ __("This action can not be undone. Do you want to continue?") }}',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '{{ __("Yes") }}',
            cancelButtonText: '{{ __("No") }}',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                deleteStage(stageId, stageName);
            }
        });
    }
}


// Initialize dragula for the Kanban board
function initializeDragula() {
    if (typeof dragula !== 'undefined') {
        // Clear any stuck loading states first
        $('.kanban-task-card').removeClass('updating-stage dragging').css('opacity', '1');
        
        // Destroy existing dragula instance if it exists
        if (window.kanbanDragula) {
            console.log('Destroying existing dragula instance');
            try {
                window.kanbanDragula.destroy();
            } catch (e) {
                console.warn('Error destroying dragula:', e);
            }
            window.kanbanDragula = null;
        }
        
        // Clear any pending task updates
        window.pendingTaskUpdates = {};
        
        // Wait a bit for DOM to be ready
        setTimeout(function() {
            // Double-check that we don't already have an instance
            if (window.kanbanDragula) {
                console.log('Dragula instance already exists, skipping initialization');
                return;
            }
            
            // Get all task containers
            var containers = document.querySelectorAll('.kanban-tasks');
            console.log('Found containers for dragula:', containers.length);
            
            if (containers.length > 0) {
                console.log('Initializing dragula with containers:', containers);
                window.kanbanDragula = dragula(Array.from(containers), {
                    moves: function(el, container, handle) {
                        // Check if user has permission to move tasks
                        var hasPermission = {{ \Auth::user()->can('view project task') || \Auth::user()->can('edit project task') ? 'true' : 'false' }};
                        if (!hasPermission) {
                            console.log('No permission to move tasks');
                            return false;
                        }
                        // Don't allow dragging if the element is currently updating
                        if ($(el).hasClass('updating-stage')) {
                            console.log('Element is updating, cannot drag');
                            return false;
                        }
                        var canDrag = handle.classList.contains('task-card-draggable') || handle.closest('.task-card-draggable');
                        console.log('Can drag element:', canDrag, el);
                        return canDrag;
                    },
                    accepts: function(el, target, source, sibling) {
                        var canAccept = target.classList.contains('kanban-tasks');
                        console.log('Can accept drop:', canAccept, target);
                        return canAccept;
                    },
                    direction: 'vertical',
                    copy: false,
                    copySortSource: false,
                    revertOnSpill: true,
                    removeOnSpill: false,
                    mirrorContainer: document.body
                });
                
                console.log('Dragula initialized successfully:', window.kanbanDragula);
                
                // Handle drop events
                window.kanbanDragula.on('drop', function(el, target, source, sibling) {
                    console.log('Drop event triggered:', {
                        element: el,
                        target: target,
                        source: source,
                        timestamp: new Date().getTime()
                    });
                    
                    var taskId = el.getAttribute('data-task-id');
                    var newStageId = target.getAttribute('data-stage-id');
                    var oldStageId = source.getAttribute('data-stage-id');
                    var projectId = el.getAttribute('data-project-id');
                    
                    console.log('Drop data:', {
                        taskId: taskId,
                        newStageId: newStageId,
                        oldStageId: oldStageId,
                        projectId: projectId
                    });
                    
                    // Only proceed if the stage actually changed
                    if (newStageId !== oldStageId && taskId && newStageId && oldStageId) {
                        // Check if this element is already being updated
                        if ($(el).hasClass('updating-stage')) {
                            console.log('Task already being updated, skipping duplicate drop');
                            return;
                        }
                        
                        // Add loading state to the dragged element
                        $(el).addClass('updating-stage').css('opacity', '0.7');
                        updateTaskStage(taskId, newStageId, oldStageId, el);
                    } else {
                        console.log('Drop ignored - no stage change or missing data:', {
                            stageChanged: newStageId !== oldStageId,
                            hasTaskId: !!taskId,
                            hasNewStageId: !!newStageId,
                            hasOldStageId: !!oldStageId
                        });
                    }
                });
                
                // Handle drag start to prevent issues
                window.kanbanDragula.on('drag', function(el, source) {
                    console.log('Drag started:', el);
                    // Clear any existing loading states before starting new drag
                    $('.kanban-task-card').removeClass('updating-stage').css('opacity', '1');
                    $(el).addClass('dragging');
                });
                
                // Handle drag end
                window.kanbanDragula.on('dragend', function(el) {
                    console.log('Drag ended:', el);
                    $(el).removeClass('dragging');
                    // Clear loading state if no update is happening
                    if (!$(el).hasClass('updating-stage')) {
                        $(el).css('opacity', '1');
                    }
                });
                
                // Handle cancel (when drag is cancelled)
                window.kanbanDragula.on('cancel', function(el, container, source) {
                    console.log('Drag cancelled:', el);
                    $(el).removeClass('dragging updating-stage').css('opacity', '1');
                });
                
                // Handle over (when dragging over a container)
                window.kanbanDragula.on('over', function(el, container, source) {
                    $(container).addClass('drag-over');
                });
                
                // Handle out (when dragging out of a container)
                window.kanbanDragula.on('out', function(el, container, source) {
                    $(container).removeClass('drag-over');
                });
            } else {
                console.warn('No containers found for dragula initialization');
            }
        }, 100);
    } else {
        console.error('Dragula library not available');
    }
}

// Update task stage via AJAX
function updateTaskStage(taskId, newStageId, oldStageId, draggedElement) {
    // Prevent duplicate calls for the same task
    var operationKey = taskId + '-' + newStageId + '-' + oldStageId;
    if (window.pendingTaskUpdates && window.pendingTaskUpdates[operationKey]) {
        console.log('Duplicate task update prevented:', operationKey);
        return;
    }
    
    // Initialize pending updates tracker
    if (!window.pendingTaskUpdates) {
        window.pendingTaskUpdates = {};
    }
    
    // Mark this operation as pending
    window.pendingTaskUpdates[operationKey] = true;
    
    // Set a timeout to clear loading state if AJAX takes too long
    var loadingTimeout = setTimeout(function() {
        if (draggedElement) {
            $(draggedElement).removeClass('updating-stage').css('opacity', '1');
        }
        // Clear pending operation
        delete window.pendingTaskUpdates[operationKey];
        show_toastr('Warning', '{{ __("Task update is taking longer than expected") }}', 'warning');
    }, 10000); // 10 second timeout
    
    console.log('Updating task stage:', { taskId, newStageId, oldStageId, operationKey });
    
    $.ajax({
        url: '{{ route("projects.tasks.update.stage") }}',
        method: 'POST',
        data: {
            task_id: taskId,
            stage_id: newStageId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            clearTimeout(loadingTimeout);
            // Clear pending operation
            delete window.pendingTaskUpdates[operationKey];
            
            console.log('Task stage update response:', response);
            
            if (response.success) {
                show_toastr('Success', response.message, 'success');
                
                // Update stage counters immediately
                updateStageCounters();
                
                // Remove loading state from the dragged element
                if (draggedElement) {
                    $(draggedElement).removeClass('updating-stage').css('opacity', '1');
                }
                
                // Use smart refresh to avoid conflicts with other operations
                smartRefresh('task-drag', 500);
                
            } else {
                show_toastr('Error', response.message || '{{ __("Failed to update task stage") }}', 'error');
                // Remove loading state
                if (draggedElement) {
                    $(draggedElement).removeClass('updating-stage').css('opacity', '1');
                }
                // Revert the task to original position
                smartRefresh('task-drag-error', 200);
            }
        },
        error: function(xhr, status, error) {
            clearTimeout(loadingTimeout);
            // Clear pending operation
            delete window.pendingTaskUpdates[operationKey];
            
            console.log('Task stage update error:', { status: xhr.status, error });
            
            var errorMessage = '{{ __("Failed to update task stage") }}';
            
            // Try to get more specific error message
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 403) {
                errorMessage = '{{ __("Permission denied. You may not have the required permissions to move tasks.") }}';
            } else if (xhr.status === 419) {
                errorMessage = '{{ __("Session expired. Please refresh the page and try again.") }}';
            } else if (xhr.status === 422) {
                errorMessage = '{{ __("Invalid data. Please check your input and try again.") }}';
            } else if (xhr.status === 500) {
                errorMessage = '{{ __("Server error. Please try again later.") }}';
            }
            
            show_toastr('Error', errorMessage, 'error');
            // Remove loading state
            if (draggedElement) {
                $(draggedElement).removeClass('updating-stage').css('opacity', '1');
            }
            // Revert the task to original position
            smartRefresh('task-drag-error', 200);
        }
    });
}

// Update stage counters
function updateStageCounters() {
    $('.kanban-column').each(function() {
        var stageId = $(this).find('.kanban-tasks').attr('data-stage-id');
        var taskCount = $(this).find('.kanban-task-card').length;
        $(this).find('.badge').text(taskCount);
    });
}

// Clear all loading states
function clearAllLoadingStates() {
    $('.kanban-task-card').removeClass('updating-stage dragging').css('opacity', '1');
    $('.kanban-column').removeClass('loading');
    $('.kanban-tasks').removeClass('drag-over');
    
    // Clear all pending operations
    window.pendingTaskUpdates = {};
    window.pendingStageUpdates = {};
    window.pendingStageCreation = null;
    
    console.log('All loading states and pending operations cleared');
}

// Force reinitialize dragula and clear all states
function forceReinitializeDragula() {
    clearAllLoadingStates();
    
    // Destroy existing dragula instance
    if (window.kanbanDragula) {
        window.kanbanDragula.destroy();
        window.kanbanDragula = null;
    }
    
    // Reinitialize after a short delay
    setTimeout(function() {
        initializeDragula();
        updateStageCounters();
    }, 100);
}

// Function to test dragula functionality
function testDragula() {
    console.log('Testing dragula functionality...');
    console.log('Dragula instance:', window.kanbanDragula);
    console.log('Dragula containers:', window.kanbanDragula ? window.kanbanDragula.containers : 'No dragula instance');
    
    var taskCards = document.querySelectorAll('.task-card-draggable');
    console.log('Found task cards:', taskCards.length);
    
    var containers = document.querySelectorAll('.kanban-tasks');
    console.log('Found containers:', containers.length);
    
    // Test if elements are draggable
    taskCards.forEach(function(card, index) {
        console.log('Task card ' + index + ':', {
            id: card.getAttribute('data-task-id'),
            classes: card.className,
            draggable: card.draggable
        });
    });
    
    return {
        dragula: !!window.kanbanDragula,
        containers: containers.length,
        taskCards: taskCards.length
    };
}

// Initialize on document ready
$(document).ready(function() {
    console.log('Kanban content loaded, initializing...');
    
    // Initialize dragula
    initializeDragula();
    
    // Initialize stage form handlers
    initializeStageFormHandlers();
    
    // Initialize task card click handlers
    initializeTaskCardHandlers();
    
    // Update stage counters
    updateStageCounters();
    
    // Ensure CSRF token is available
    ensureCSRFToken();
});

// Also initialize when the page is fully loaded
$(window).on('load', function() {
    setTimeout(function() {
        if (typeof dragula !== 'undefined' && !window.kanbanDragula) {
            initializeDragula();
            updateStageCounters();
        }
    }, 500);
});

// Global event listener to handle stuck states
$(document).on('click', '.kanban-task-card.updating-stage', function(e) {
    // If user clicks on a stuck loading card, force clear the state
    $(this).removeClass('updating-stage').css('opacity', '1');
    show_toastr('Info', '{{ __("Loading state cleared. You can now drag the task again.") }}', 'info');
});

// Function to manually refresh the Kanban board
function manualRefresh() {
    console.log('Manual refresh requested');
    clearAllLoadingStates();
    
    // Cancel any pending smart refresh
    if (window.refreshTimeout) {
        clearTimeout(window.refreshTimeout);
    }
    
    // Force immediate refresh
    refreshKanbanBoard();
}
</script>

<style>
/* Kanban Board Horizontal Scrolling Styles */
.kanban-container {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0;
    margin: 0;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.kanban-wrapper {
    display: flex;
    gap: 1rem;
    min-width: max-content;
    padding: 0.5rem;
    height: calc(100vh - 300px); /* Adjust height as needed */
}

.kanban-column {
    flex: 0 0 320px; /* Fixed width for each column */
    min-width: 320px;
    max-width: 320px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.kanban-header {
    padding: 1rem;
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
    flex-shrink: 0;
}

.kanban-tasks {
    flex: 1;
    padding: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 200px;
}

.kanban-task-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: grab;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.kanban-task-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.kanban-task-card:active {
    cursor: grabbing;
}

.kanban-task-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.kanban-task-card.updating-stage {
    opacity: 0.7;
    pointer-events: none;
}

.kanban-tasks.drag-over {
    background-color: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
    border-radius: 6px;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.task-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
    line-height: 1.3;
}

.task-title a {
    color: #495057;
    text-decoration: none;
}

.task-title a:hover {
    color: #007bff;
}

.task-priority-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-weight: 500;
    text-transform: uppercase;
    margin-left: 0.5rem;
    flex-shrink: 0;
}

.priority-critical {
    background: #dc3545;
    color: #fff;
}

.priority-high {
    background: #fd7e14;
    color: #fff;
}

.priority-medium {
    background: #ffc107;
    color: #212529;
}

.priority-low {
    background: #6c757d;
    color: #fff;
}

.task-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin: 0.5rem 0;
    line-height: 1.4;
}

.task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #f1f3f4;
}

.task-assignees {
    display: flex;
    gap: 0.25rem;
}

.task-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #007bff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.task-date {
    font-size: 0.75rem;
    color: #6c757d;
}

.task-stats {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #f1f3f4;
}

.task-stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.7rem;
    color: #6c757d;
}

.task-stat i {
    font-size: 0.8rem;
}

.empty-stage {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120px;
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    color: #6c757d;
    text-align: center;
}

.empty-stage-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

.empty-stage-text {
    font-size: 0.8rem;
    margin: 0;
    opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kanban-wrapper {
        gap: 0.5rem;
        padding: 0.25rem;
    }
    
    .kanban-column {
        flex: 0 0 280px;
        min-width: 280px;
        max-width: 280px;
    }
    
    .kanban-header {
        padding: 0.75rem;
    }
    
    .kanban-tasks {
        padding: 0.25rem;
    }
}

/* Custom scrollbar for webkit browsers */
.kanban-container::-webkit-scrollbar {
    height: 8px;
}

.kanban-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.kanban-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.kanban-tasks::-webkit-scrollbar {
    width: 6px;
}

.kanban-tasks::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.kanban-tasks::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.kanban-tasks::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
