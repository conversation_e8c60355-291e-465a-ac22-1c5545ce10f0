<!DOCTYPE html>
<html>
<head>
    <title>Test Custom Field Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg">
</head>
<body>
    <div class="container mt-5">
        <h2>Custom Field Modal Test</h2>
        
        <!-- Test Form -->
        <form>
            <div class="row">
                <div class="form-group col-md-6">
                    <label class="form-label">Field Type</label>
                    <select class="form-control" id="type">
                        <option value="">Select Field Type</option>
                        <option value="text">Text</option>
                        <option value="email">Email</option>
                        <option value="select">Dropdown menu</option>
                        <option value="radio">Radio button group</option>
                        <option value="checkbox">Single or multiple checkbox options</option>
                        <option value="multiselect">Dropdown with multiple selection</option>
                        <option value="textarea">Textarea</option>
                        <option value="number">Number</option>
                    </select>
                </div>
            </div>
            
            <!-- Dynamic Options Container -->
            <div class="form-group col-md-12" id="options-container" style="display:none;">
                <label class="form-label">Options (for dropdown/select/checkbox/multiselect)</label>
                <small class="form-text text-muted mb-2">Add options for your field. Each option will be available for selection.</small>
                <div id="options-list">
                    <div class="d-flex mb-2 option-row">
                        <input type="text" name="options[]" class="form-control me-2" placeholder="Enter option value" required="false">
                        <button type="button" class="btn btn-sm btn-success add-option" title="Add Option">
                            +
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="me-1">ℹ️</i>
                        Click the + button to add more options. At least one option is required for these field types.
                    </small>
                </div>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const typeSelect = document.getElementById('type');
            const optionsContainer = document.getElementById('options-container');
            const optionsList = document.getElementById('options-list');

            // Field types that require options (only these will show the dynamic options field)
            const optionFieldTypes = ['select', 'radio', 'checkbox', 'multiselect'];

            function toggleOptions() {
                const selected = typeSelect.value;
                console.log('Selected field type:', selected);
                
                if (optionFieldTypes.includes(selected)) {
                    console.log('Showing options container');
                    optionsContainer.style.display = 'block';
                    // Ensure at least one option field exists
                    if (optionsList.children.length === 0) {
                        addDefaultOptionInput();
                    }
                } else {
                    console.log('Hiding options container');
                    optionsContainer.style.display = 'none';
                    // Clear all options when not needed
                    optionsList.innerHTML = '';
                    // Reset to default single option field for next time
                    addDefaultOptionInput();
                }
            }

            function addDefaultOptionInput() {
                const inputDiv = document.createElement('div');
                inputDiv.className = 'd-flex mb-2 option-row';
                inputDiv.innerHTML = `
                    <input type="text" name="options[]" class="form-control me-2" placeholder="Enter option value" required="false">
                    <button type="button" class="btn btn-sm btn-success add-option" title="Add Option">
                        +
                    </button>
                `;
                optionsList.appendChild(inputDiv);
            }

            function addOptionInput() {
                const optionCount = optionsList.children.length + 1;
                const inputDiv = document.createElement('div');
                inputDiv.className = 'd-flex mb-2 option-row';
                inputDiv.innerHTML = `
                    <input type="text" name="options[]" class="form-control me-2" placeholder="Enter option value ${optionCount}" required="false">
                    <button type="button" class="btn btn-sm btn-danger remove-option" title="Remove Option">
                        -
                    </button>
                `;
                optionsList.appendChild(inputDiv);
                
                // Update the first option's button to add button if it was the only one
                updateButtonStates();
            }

            function updateButtonStates() {
                const optionRows = optionsList.querySelectorAll('.option-row');
                
                optionRows.forEach((row, index) => {
                    const button = row.querySelector('button');
                    if (optionRows.length === 1) {
                        // Only one option - show add button
                        button.className = 'btn btn-sm btn-success add-option';
                        button.innerHTML = '+';
                        button.title = 'Add Option';
                    } else if (index === 0) {
                        // First option - show add button
                        button.className = 'btn btn-sm btn-success add-option';
                        button.innerHTML = '+';
                        button.title = 'Add Option';
                    } else {
                        // Other options - show remove button
                        button.className = 'btn btn-sm btn-danger remove-option';
                        button.innerHTML = '-';
                        button.title = 'Remove Option';
                    }
                });
            }

            // Event delegation for dynamic buttons
            optionsList.addEventListener('click', function (e) {
                const button = e.target.closest('button');
                if (!button) return;

                if (button.classList.contains('remove-option')) {
                    e.preventDefault();
                    const row = button.closest('.option-row');
                    row.remove();
                    updateButtonStates();
                    
                    // Ensure at least one option exists for option field types
                    if (optionsList.children.length === 0 && optionFieldTypes.includes(typeSelect.value)) {
                        addDefaultOptionInput();
                        updateButtonStates();
                    }
                } else if (button.classList.contains('add-option')) {
                    e.preventDefault();
                    addOptionInput();
                }
            });

            // Initialize on type change
            typeSelect.addEventListener('change', toggleOptions);
            
            // Initialize on page load
            toggleOptions();
        });
    </script>
</body>
</html>
