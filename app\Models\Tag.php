<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tag extends Model
{
    protected $fillable = [
        'name',
        'color',
        'created_by',
        'is_active',
    ];

    /**
     * Get leads that have this tag
     */
    public function leads()
    {
        return \App\Models\Lead::where('created_by', $this->created_by)
                              ->whereRaw("FIND_IN_SET(?, tags)", [$this->id])
                              ->get();
    }

    /**
     * Get projects that have this tag  
     */
    public function projects()
    {
        return \App\Models\Project::where('created_by', $this->created_by)
                                 ->whereRaw("FIND_IN_SET(?, tags)", [$this->id])
                                 ->get();
    }

    /**
     * Scope to get active tags only
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    /**
     * Scope to get tags for current user
     */
    public function scopeForUser($query, $userId = null)
    {
        $userId = $userId ?: \Auth::user()->creatorId();
        return $query->where('created_by', $userId);
    }
} 