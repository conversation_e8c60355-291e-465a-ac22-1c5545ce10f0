<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Centering Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f2f5;
            padding: 20px;
            margin: 0;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .test-title {
            text-align: center;
            color: #1e3a8a;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: bold;
        }

        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .icon-test {
            text-align: center;
        }

        .icon-label {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }

        /* 3D Icon Container */
        .dash-micon {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 18px;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
            box-shadow: 
                0 8px 25px rgba(30, 58, 138, 0.4),
                inset 0 3px 0 rgba(255, 255, 255, 0.25),
                inset 0 -3px 0 rgba(0, 0, 0, 0.15);
            margin: 0 auto 10px;
            transition: all 0.3s ease;
        }

        .dash-micon:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 
                0 12px 35px rgba(30, 58, 138, 0.5),
                inset 0 3px 0 rgba(255, 255, 255, 0.3),
                inset 0 -3px 0 rgba(0, 0, 0, 0.2);
        }

        /* SVG Icon Styles */
        .dash-micon svg {
            width: 24px;
            height: 24px;
            stroke: #ffffff;
            fill: none;
            stroke-width: 2.5;
            stroke-linecap: round;
            stroke-linejoin: round;
            display: block;
            flex-shrink: 0;
        }

        /* Grid lines for visual reference */
        .dash-micon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255,255,255,0.2);
            z-index: 1;
        }

        .dash-micon::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(255,255,255,0.2);
            z-index: 1;
        }

        .info {
            background: #e0f2fe;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #0288d1;
            margin-top: 20px;
        }

        .info h3 {
            margin: 0 0 10px 0;
            color: #0277bd;
        }

        .info p {
            margin: 5px 0;
            color: #01579b;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎯 Icon Centering Test</h1>
        
        <div class="icon-grid">
            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                </div>
                <div class="icon-label">Home</div>
            </div>

            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                        <path d="m22 12-10-10v10z"/>
                    </svg>
                </div>
                <div class="icon-label">Chart</div>
            </div>

            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <rect x="4" y="2" width="16" height="20" rx="2"/>
                        <line x1="8" y1="6" x2="16" y2="6"/>
                        <line x1="8" y1="10" x2="16" y2="10"/>
                        <line x1="8" y1="14" x2="16" y2="14"/>
                        <line x1="8" y1="18" x2="16" y2="18"/>
                    </svg>
                </div>
                <div class="icon-label">Calculator</div>
            </div>

            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <circle cx="8" cy="8" r="6"/>
                        <path d="M18.09 10.37A6 6 0 1 1 10.34 18"/>
                        <path d="M7 6h1v4"/>
                    </svg>
                </div>
                <div class="icon-label">Coins</div>
            </div>

            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/>
                    </svg>
                </div>
                <div class="icon-label">Users</div>
            </div>

            <div class="icon-test">
                <div class="dash-micon">
                    <svg viewBox="0 0 24 24">
                        <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/>
                        <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/>
                    </svg>
                </div>
                <div class="icon-label">Rocket</div>
            </div>
        </div>

        <div class="info">
            <h3>✅ Centering Test Results</h3>
            <p><strong>Method:</strong> CSS Flexbox (display: flex, align-items: center, justify-content: center)</p>
            <p><strong>Icon Size:</strong> 24px × 24px</p>
            <p><strong>Container Size:</strong> 60px × 60px</p>
            <p><strong>Visual Guides:</strong> Cross-hair lines show exact center</p>
            <p><strong>Expected Result:</strong> Icons should be perfectly centered on the intersection of the guide lines</p>
        </div>
    </div>
</body>
</html>
