<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Menu Icons Demo - OMX SaaS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            text-align: center;
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .demo-subtitle {
            text-align: center;
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            border: 1px solid #e2e8f0;
        }

        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            background: rgba(59, 130, 246, 0.05);
        }

        .menu-item.active {
            background: rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* 3D Icon Styles */
        .dash-micon {
            position: relative !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 50px !important;
            height: 50px !important;
            min-width: 50px !important;
            min-height: 50px !important;
            border-radius: 15px !important;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%) !important;
            box-shadow:
                0 6px 20px rgba(30, 58, 138, 0.4),
                inset 0 2px 0 rgba(255, 255, 255, 0.25),
                inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            margin-right: 18px !important;
            overflow: hidden !important;
            flex-shrink: 0 !important;
        }

        .dash-micon::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%) !important;
            border-radius: inherit !important;
            transition: opacity 0.3s ease !important;
            pointer-events: none !important;
        }

        .dash-micon i {
            position: relative !important;
            z-index: 10 !important;
            color: #ffffff !important;
            font-size: 24px !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.3s ease !important;
            font-weight: 500 !important;
            line-height: 1 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* Active State */
        .menu-item.active .dash-micon {
            transform: scale(1.12) !important;
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 50%, #3b82f6 100%) !important;
            box-shadow:
                0 12px 35px rgba(29, 78, 216, 0.6),
                inset 0 3px 0 rgba(255, 255, 255, 0.35),
                inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
        }

        .menu-item.active .dash-micon i {
            transform: scale(1.1) !important;
            color: #ffffff !important;
            text-shadow: 0 3px 8px rgba(0, 0, 0, 0.6) !important;
            font-weight: 600 !important;
            font-size: 26px !important;
        }

        /* Hover Effects */
        .menu-item:hover .dash-micon {
            transform: translateY(-4px) scale(1.06) !important;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #60a5fa 100%) !important;
            box-shadow:
                0 10px 30px rgba(37, 99, 235, 0.5),
                inset 0 3px 0 rgba(255, 255, 255, 0.35),
                inset 0 -3px 0 rgba(0, 0, 0, 0.2) !important;
        }

        .menu-item:hover .dash-micon::before {
            opacity: 1 !important;
        }

        .menu-item:hover .dash-micon i {
            transform: scale(1.05) !important;
            color: #ffffff !important;
            text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5) !important;
            font-size: 25px !important;
        }

        .dash-mtext {
            font-weight: 500;
            color: #374151;
            font-size: 1rem;
            transition: color 0.3s ease;
        }

        .menu-item.active .dash-mtext {
            color: #1d4ed8;
            font-weight: 600;
        }

        .menu-item:hover .dash-mtext {
            color: #1d4ed8;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .dash-micon {
                width: 46px !important;
                height: 46px !important;
                min-width: 46px !important;
                min-height: 46px !important;
            }

            .dash-micon i {
                font-size: 22px !important;
                color: #ffffff !important;
            }
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }

            .demo-title {
                font-size: 2rem;
            }

            .menu-grid {
                grid-template-columns: 1fr;
            }

            .dash-micon {
                width: 44px !important;
                height: 44px !important;
                min-width: 44px !important;
                min-height: 44px !important;
                margin-right: 14px !important;
            }

            .dash-micon i {
                font-size: 20px !important;
                color: #ffffff !important;
            }
        }

        @media (max-width: 480px) {
            .dash-micon {
                width: 40px !important;
                height: 40px !important;
                min-width: 40px !important;
                min-height: 40px !important;
                margin-right: 12px !important;
            }

            .dash-micon i {
                font-size: 18px !important;
                color: #ffffff !important;
            }
        }

        .demo-info {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #3b82f6;
            margin-top: 30px;
        }

        .demo-info h3 {
            color: #1e40af;
            margin-bottom: 10px;
        }

        .demo-info p {
            color: #64748b;
            line-height: 1.6;
        }

        /* Force icon visibility */
        .dash-micon,
        .dash-micon i {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .dash-micon i {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", inherit !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-rendering: auto !important;
            -webkit-font-smoothing: antialiased !important;
        }

        /* Ensure FontAwesome icons are properly loaded */
        .fas, .fab {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
        }

        .fab {
            font-family: "Font Awesome 6 Brands" !important;
            font-weight: 400 !important;
        }

        /* Ensure icons are visible with fallbacks */
        .dash-micon i {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Apple Color Emoji", "Segoe UI Emoji" !important;
        }

        /* SVG Icon Styles - Flexbox Centering */
        .dash-micon svg {
            width: 22px !important;
            height: 22px !important;
            stroke: #ffffff !important;
            fill: none !important;
            stroke-width: 2.5 !important;
            stroke-linecap: round !important;
            stroke-linejoin: round !important;
            display: block !important;
            flex-shrink: 0 !important;
        }

        /* Force icon display */
        .dash-micon i::before {
            display: inline-block !important;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">3D Menu Icons Demo</h1>
        <p class="demo-subtitle">Modern 3D icons with dark blue gradients and responsive design</p>
        
        <div class="menu-grid">
            <div class="menu-item active">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                </span>
                <span class="dash-mtext">Dashboard</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                        <path d="m22 12-10-10v10z"/>
                    </svg>
                </span>
                <span class="dash-mtext">Reports | Analytics</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="4" y="2" width="16" height="20" rx="2"/>
                        <line x1="8" y1="6" x2="16" y2="6"/>
                        <line x1="16" y1="10" x2="16" y2="10"/>
                        <line x1="12" y1="10" x2="12" y2="10"/>
                        <line x1="8" y1="10" x2="8" y2="10"/>
                        <line x1="16" y1="14" x2="16" y2="14"/>
                        <line x1="12" y1="14" x2="12" y2="14"/>
                        <line x1="8" y1="14" x2="8" y2="14"/>
                        <line x1="16" y1="18" x2="16" y2="18"/>
                        <line x1="12" y1="18" x2="12" y2="18"/>
                        <line x1="8" y1="18" x2="8" y2="18"/>
                    </svg>
                </span>
                <span class="dash-mtext">Accounting System</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="8" cy="8" r="6"/>
                        <path d="M18.09 10.37A6 6 0 1 1 10.34 18"/>
                        <path d="M7 6h1v4"/>
                        <path d="m16.71 13.88.7.71-2.82 2.82"/>
                    </svg>
                </span>
                <span class="dash-mtext">Finance</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/>
                    </svg>
                </span>
                <span class="dash-mtext">Lead Tracker</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/>
                        <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/>
                        <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/>
                        <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/>
                    </svg>
                </span>
                <span class="dash-mtext">Mission Control</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9 11H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m0-7v7m0-7a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2m0 0H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m6-10V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5"/>
                    </svg>
                </span>
                <span class="dash-mtext">My Tasks</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                        <line x1="16" y1="2" x2="16" y2="6"/>
                        <line x1="8" y1="2" x2="8" y2="6"/>
                        <line x1="3" y1="10" x2="21" y2="10"/>
                    </svg>
                </span>
                <span class="dash-mtext">Smart Scheduler</span>
            </div>
            
            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                        <circle cx="9" cy="7" r="4"/>
                        <path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/>
                    </svg>
                </span>
                <span class="dash-mtext">User Management</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/>
                        <path d="m3.3 7 8.7 5 8.7-5"/>
                        <path d="M12 22V12"/>
                    </svg>
                </span>
                <span class="dash-mtext">Products System</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"/>
                        <circle cx="12" cy="12" r="4"/>
                        <line x1="4.93" y1="4.93" x2="9.17" y2="9.17"/>
                        <line x1="14.83" y1="14.83" x2="19.07" y2="19.07"/>
                        <line x1="14.83" y1="9.17" x2="19.07" y2="4.93"/>
                        <line x1="4.93" y1="19.07" x2="9.17" y2="14.83"/>
                    </svg>
                </span>
                <span class="dash-mtext">Support System</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"/>
                        <path d="M9 10a3 3 0 0 1 6 0c0 2-3 3-3 3"/>
                        <path d="M9 17h.01"/>
                    </svg>
                </span>
                <span class="dash-mtext">WhatsApp Flow</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                        <polyline points="14,2 14,8 20,8"/>
                    </svg>
                </span>
                <span class="dash-mtext">Templates</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m3 11 18-5v12L3 14v-3z"/>
                        <path d="M11.6 16.8a3 3 0 1 1-5.8-1.6"/>
                    </svg>
                </span>
                <span class="dash-mtext">Campaigns</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="8" cy="21" r="1"/>
                        <circle cx="19" cy="21" r="1"/>
                        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
                    </svg>
                </span>
                <span class="dash-mtext">WhatsApp Orders</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
                        <circle cx="12" cy="5" r="2"/>
                        <path d="M12 7v4"/>
                        <line x1="8" y1="16" x2="8" y2="16"/>
                        <line x1="16" y1="16" x2="16" y2="16"/>
                    </svg>
                </span>
                <span class="dash-mtext">Chatbot</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22,12 18,12 15,21 9,21 6,12 2,12"/>
                        <path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"/>
                    </svg>
                </span>
                <span class="dash-mtext">Unified Inbox</span>
            </div>

            <div class="menu-item">
                <span class="dash-micon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="4" y1="21" x2="4" y2="14"/>
                        <line x1="4" y1="10" x2="4" y2="3"/>
                        <line x1="12" y1="21" x2="12" y2="12"/>
                        <line x1="12" y1="8" x2="12" y2="3"/>
                        <line x1="20" y1="21" x2="20" y2="16"/>
                        <line x1="20" y1="12" x2="20" y2="3"/>
                        <line x1="1" y1="14" x2="7" y2="14"/>
                        <line x1="9" y1="8" x2="15" y2="8"/>
                        <line x1="17" y1="16" x2="23" y2="16"/>
                    </svg>
                </span>
                <span class="dash-mtext">System Settings</span>
            </div>
        </div>
        
        <div class="demo-info">
            <h3>Features Implemented:</h3>
            <p>✅ Modern 3D icons with dark blue gradients<br>
            ✅ Hover effects with scale and shadow animations<br>
            ✅ Active state with enhanced visual feedback<br>
            ✅ Responsive design for mobile and desktop<br>
            ✅ Smooth transitions and cubic-bezier animations<br>
            ✅ Professional color scheme matching the reference image</p>
        </div>
    </div>

    <script>
        // Add click functionality to demonstrate active states
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
