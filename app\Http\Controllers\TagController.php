<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use App\Models\Lead;
use App\Models\Project;
use Illuminate\Http\Request;

class TagController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth',
            'XSS',
        ]);
    }

    /**
     * Display a listing of tags in system settings
     */
    public function index()
    {
        $tags = Tag::where('created_by', \Auth::user()->creatorId())
                   ->orderBy('name', 'asc')
                   ->get();
        
        return view('tags.index', compact('tags'));
    }

    /**
     * Show the form for creating a new tag
     */
    public function create()
    {
        return view('tags.create');
    }

    /**
     * Show the form for editing the specified tag
     */
    public function edit(Tag $tag)
    {
        if($tag->created_by == \Auth::user()->creatorId()) {
            return view('tags.edit', compact('tag'));
        } else {
            return response()->json(['error' => __('Permission Denied.')], 403);
        }
    }

    /**
     * Store a newly created tag
     */
    public function store(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:tags,name,NULL,id,created_by,' . \Auth::user()->creatorId(),
        ]);

        if($validator->fails()) {
            $messages = $validator->getMessageBag();
            if($request->ajax()) {
                return response()->json(['error' => $messages->first()], 422);
            }
            return redirect()->back()->with('error', $messages->first());
        }

        $tag = new Tag();
        $tag->name = $request->name;
        $tag->created_by = \Auth::user()->creatorId();
        $tag->is_active = 1;
        $tag->save();

        if($request->ajax()) {
            return response()->json([
                'success' => __('Tag successfully created!'),
                'tag' => $tag
            ]);
        }

        return redirect()->back()->with('success', __('Tag successfully created!'));
    }

    /**
     * Update the specified tag
     */
    public function update(Request $request, Tag $tag)
    {
        if($tag->created_by == \Auth::user()->creatorId()) {
            $validator = \Validator::make($request->all(), [
                'name' => 'required|string|max:50|unique:tags,name,' . $tag->id . ',id,created_by,' . \Auth::user()->creatorId(),
            ]);

            if($validator->fails()) {
                $messages = $validator->getMessageBag();
                if($request->ajax()) {
                    return response()->json(['error' => $messages->first()], 422);
                }
                return redirect()->back()->with('error', $messages->first());
            }

            $tag->name = $request->name;
            $tag->save();

            if($request->ajax()) {
                return response()->json([
                    'success' => __('Tag successfully updated!'),
                    'tag' => $tag
                ]);
            }

            return redirect()->back()->with('success', __('Tag successfully updated!'));
        } else {
            if($request->ajax()) {
                return response()->json(['error' => __('Permission Denied.')], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Remove the specified tag
     */
    public function destroy(Request $request, Tag $tag)
    {
        if($tag->created_by == \Auth::user()->creatorId()) {
            // Remove tag ID from all leads that have it assigned
            $leads = \App\Models\Lead::where('created_by', \Auth::user()->creatorId())
                                   ->whereNotNull('tags')
                                   ->where('tags', '!=', '')
                                   ->get();

            foreach($leads as $lead) {
                if($lead->tags) {
                    $tagIds = explode(',', $lead->tags);
                    $tagIds = array_filter($tagIds, function($id) use ($tag) {
                        return $id != $tag->id;
                    });
                    $lead->tags = !empty($tagIds) ? implode(',', $tagIds) : null;
                    $lead->save();
                }
            }

            // Remove tag ID from all projects that have it assigned
            $projects = \App\Models\Project::where('created_by', \Auth::user()->creatorId())
                                         ->whereNotNull('tags')
                                         ->where('tags', '!=', '')
                                         ->get();

            foreach($projects as $project) {
                if($project->tags) {
                    $tagIds = explode(',', $project->tags);
                    $tagIds = array_filter($tagIds, function($id) use ($tag) {
                        return $id != $tag->id;
                    });
                    $project->tags = !empty($tagIds) ? implode(',', $tagIds) : null;
                    $project->save();
                }
            }

            $tag->delete();

            if($request->ajax()) {
                return response()->json(['success' => __('Tag successfully deleted!')]);
            }

            return redirect()->back()->with('success', __('Tag successfully deleted!'));
        } else {
            if($request->ajax()) {
                return response()->json(['error' => __('Permission Denied.')], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Toggle tag active status
     */
    public function toggleStatus(Request $request, Tag $tag)
    {
        if($tag->created_by == \Auth::user()->creatorId()) {
            $tag->is_active = !$tag->is_active;
            $tag->save();

            if($request->ajax()) {
                return response()->json([
                    'success' => __('Tag status updated!'),
                    'status' => $tag->is_active
                ]);
            }

            return redirect()->back()->with('success', __('Tag status updated!'));
        } else {
            if($request->ajax()) {
                return response()->json(['error' => __('Permission Denied.')], 403);
            }
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * Get tags for AJAX requests (for autocomplete, etc.)
     */
    public function getTags(Request $request)
    {
        $tags = Tag::where('created_by', \Auth::user()->creatorId())
                   ->where('is_active', 1)
                   ->when($request->search, function($query, $search) {
                       return $query->where('name', 'like', '%' . $search . '%');
                   })
                   ->orderBy('name', 'asc')
                   ->get(['id', 'name']);

        return response()->json($tags);
    }
} 