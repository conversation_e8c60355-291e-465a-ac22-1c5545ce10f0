@extends('layouts.admin')
@section('page-title')
    {{__('Manage Leads')}} @if($pipeline) - {{$pipeline->name}} @endif
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{asset('css/summernote/summernote-bs4.css')}}">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .communication-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .communication-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .communication-btn.call { background-color: #28a745; }
        .communication-btn.sms { background-color: #17a2b8; }
        .communication-btn.email { background-color: #6f42c1; }
        .communication-btn.source { background-color: #fd7e14; }

        .communication-btn i {
            font-size: 14px;
        }

        .modal-body .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .communication-option-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .communication-option-item i {
            font-size: 20px;
            margin-right: 15px;
            width: 25px;
            text-align: center;
        }

        .communication-option-item span {
            font-size: 16px;
            font-weight: 500;
        }

        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        .data-source-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }
        .source-internal {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .lead-checkbox {
            display: none;
            margin-right: 10px;
        }
        tr.lead-row:hover .lead-checkbox {
            display: inline-block;
        }
        tr.show-checkboxes .lead-checkbox {
            display: inline-block !important;
        }
        #bulk-delete-btn {
            display: none;
            margin-bottom: 10px;
        }
        #select-all-btn {
            display: none;
        }
        /* Oval Pipeline Select Styling */
        #default_pipeline_id {
            border-radius: 25px !important;
            padding: 8px 20px 8px 20px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%236c63ff'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e") !important;
            background-position: right 12px center !important;
            background-repeat: no-repeat !important;
            background-size: 16px 12px !important;
            padding-right: 35px !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }
        
        #default_pipeline_id:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        #default_pipeline_id:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: #ffffff !important;
        }
        
        #default_pipeline_id option {
            background: white !important;
            color: #374151 !important;
            padding: 8px 12px !important;
            border-radius: 8px !important;
        }
        
        /* Custom form wrapper styling for oval design */
        #change-pipeline {
            background: transparent !important;
            border: none !important;
            padding: 0 !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
            margin-bottom: 0px;
        }
        

        
        /* Search Bar Styling - Same oval shape as pipeline select */
        .search-container {
            position: relative !important;
            display: inline-flex !important;
            align-items: center !important;
            height: 40px !important;
        }
        
        .search-input {
            border-radius: 25px !important;
            padding: 6px 14px 6px 14px !important;
            padding-right: 45px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 200px !important;
            height: 40px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            transition: all 0.3s ease !important;
        }
        
        .search-input:focus {
            outline: none !important;
            border-color: #6c63ff !important;
            box-shadow: 0 0 0 3px rgba(108, 99, 255, 0.1) !important;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%) !important;
        }
        
        .search-input:hover {
            border-color: #6c63ff !important;
            box-shadow: 0 4px 12px rgba(108, 99, 255, 0.2) !important;
            transform: translateY(-1px) !important;
        }
        
        .search-input::placeholder {
            color: #9ca3af !important;
            font-weight: 400 !important;
        }
        
        .search-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
        }
        
        .clear-icon {
            position: absolute !important;
            right: 15px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            color: #6c63ff !important;
            font-size: 14px !important;
            cursor: pointer !important;
            z-index: 10 !important;
            transition: color 0.3s ease !important;
        }
        
        .clear-icon:hover {
            color: #dc3545 !important;
        }
        /* Select2 custom styling for oval pipeline select */
        .select2-container--default .select2-selection--single {
            border-radius: 25px !important;
            border: 2px solid #e0e7ef !important;
            background: #ffffff !important;
            color: #374151 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            min-width: 180px !important;
            height: 40px !important;
            display: flex !important;
            align-items: center !important;
            padding: 8px 20px 8px 20px !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 40px !important;
            right: 12px !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 40px !important;
        }

        /* Filter Panel Styles */
        .filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1049;
            display: none;
        }

        .filter-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100%;
            background: white;
            box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
            z-index: 1050;
            transition: right 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .filter-panel.active {
            right: 0;
        }

        .filter-view {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .filter-header {
            padding: 24px 24px 16px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f9fafb;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filter-icon {
            color: #6b7280;
            font-size: 18px;
        }

        .filter-header h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .close-btn, .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .close-btn:hover, .back-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .filter-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .filter-applied-indicator {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            background: #fbbf24;
            border-radius: 50%;
            border: 2px solid white;
            display: none;
            animation: pulse 2s infinite;
        }
        #openFilterPanel.has-filters .filter-applied-indicator {
            display: block;
        }
        #openFilterPanel.has-filters {
            background: linear-gradient(to right, #dc2626, #ef4444) !important;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Filter Categories Styles */
        .filter-categories {
            padding: 0;
        }
        .filter-category-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .filter-category-item:hover {
            background: #f9fafb;
            margin: 0 -24px;
            padding-left: 24px;
            padding-right: 24px;
        }
        .filter-category-item:last-child {
            border-bottom: none;
        }
        .category-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .category-icon {
            font-size: 16px;
            color: #6b7280;
        }
        .category-name {
            font-size: 15px;
            font-weight: 500;
            color: #374151;
        }
        .category-arrow {
            font-size: 12px;
            color: #9ca3af;
        }

        /* Filter Section Styles */
        .filter-section {
            max-height: 400px;
            overflow-y: auto;
        }
        .user-search {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
            outline: none;
            transition: border-color 0.2s ease;
        }
        .user-search:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .user-list {
            max-height: 320px;
            overflow-y: auto;
        }
        .user-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .user-item:hover {
            background: #f9fafb;
            margin: 0 -24px;
            padding-left: 24px;
            padding-right: 24px;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-item input[type="checkbox"], .user-item input[type="radio"] {
            margin-right: 12px;
            width: 16px;
            height: 16px;
        }
        .user-item label {
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            flex: 1;
            margin: 0;
        }

        /* Date Range Picker Styles */
        .date-range-picker {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .date-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        .date-separator {
            color: #6b7280;
            font-weight: 500;
        }

        /* Filter Footer Styles */
        .filter-footer {
            padding: 16px 24px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .filter-footer .btn {
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
        }
        .filter-footer .btn-link {
            color: #6b7280;
            text-decoration: none;
            background: none;
            border: none;
        }
        .filter-footer .btn-link:hover {
            color: #374151;
            text-decoration: underline;
        }
        .filter-footer .btn-secondary {
            background: #6b7280;
            border-color: #6b7280;
            color: white;
        }
        .filter-footer .btn-success {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }

        /* Action buttons responsive styles */
        .action-btn {
            transition: transform 0.2s ease-in-out;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .search-container {
                width: 100% !important;
                margin-bottom: 1rem !important;
            }
            
            .search-input {
                min-width: 100% !important;
                width: 100% !important;
            }
            
            #change-pipeline {
                width: 100% !important;
                margin-bottom: 1rem !important;
            }
            
            #default_pipeline_id {
                width: 100% !important;
                min-width: 100% !important;
            }
            
            .action-btn {
                width: 50px !important;
                height: 50px !important;
                margin-bottom: 0.5rem !important;
            }
        }

        @media (max-width: 480px) {
            .action-btn {
                width: 45px !important;
                height: 45px !important;
            }
            
            .search-input {
                font-size: 14px !important;
                padding: 8px 12px 8px 12px !important;
                padding-right: 40px !important;
            }
            
            .search-icon, .clear-icon {
                right: 12px !important;
                font-size: 12px !important;
            }
        }
    </style>
@endpush

@push('script-page')
    <!-- jQuery (if not already loaded) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{asset('css/summernote/summernote-bs4.js')}}"></script>
    <script>
        $(document).on('change', '#default_pipeline_id', function() {
            $('#change-pipeline').submit();
        });

        function openCommunicationModal(type) {
            const modal = new bootstrap.Modal(document.getElementById('communication-modal'));
            const title = document.getElementById('communication-modal-label');
            title.textContent = type === 'sms' ? 'SMS Options' : 'Email Options';
            modal.show();
        }

        function openWhatsApp(phoneNumber) {
            // Remove any non-numeric characters from phone number
            const cleanPhone = phoneNumber.replace(/\D/g, '');
            
            // Add country code if not present (assuming +1 for US, adjust as needed)
            let formattedPhone = cleanPhone;
            if (!cleanPhone.startsWith('1') && cleanPhone.length === 10) {
                formattedPhone = '1' + cleanPhone;
            }
            
            // Create WhatsApp URL
            const whatsappUrl = `https://wa.me/${formattedPhone}`;
            
            // Open WhatsApp in new tab/window
            window.open(whatsappUrl, '_blank');
        }

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '{{ __('No sources found for this lead.') }}';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }














        $(document).ready(function() {
            // Initialize Select2 after DOM is ready
            if (typeof $.fn.select2 !== 'undefined') {
                $('.select').select2({
                    placeholder: '{{__("Select Stage")}}',
                    allowClear: true
                });
            }

            // Handle off-canvas shown event
            $('#filterOffcanvas').on('shown.bs.offcanvas', function () {
                // Focus on first input when off-canvas is shown
                $(this).find('input[name="name"]').focus();
            });

            // Initialize Select2 for off-canvas filter
            if (typeof $.fn.select2 !== 'undefined') {
                $('#filterOffcanvas .select').select2({
                    dropdownParent: $('#filterOffcanvas'),
                    placeholder: '{{__("Select Stage")}}',
                    allowClear: true
                });
                
                // Initialize multiple select fields
                $('#assigned_to_filter, #tags_filter, #lead_source_filter, #contact_group_filter, #products_filter').select2({
                    dropdownParent: $('#filterOffcanvas'),
                    placeholder: 'Select options...',
                    allowClear: true
                });
            }

            // ===== BULK ACTION LOGIC =====
            var bulkDeleteInProgress = false;
            
            // Show checkboxes on row hover
            $('#leads-tbody').on('mouseenter', 'tr.lead-row', function() {
                if (!$('tr.lead-row').hasClass('show-checkboxes')) {
                    $(this).find('.lead-checkbox').show();
                }
            });
            
            $('#leads-tbody').on('mouseleave', 'tr.lead-row', function() {
                if (!$('tr.lead-row').hasClass('show-checkboxes')) {
                    $(this).find('.lead-checkbox').hide();
                }
            });
            
            // Show all checkboxes when any checkbox is clicked
            $('#leads-tbody').on('click', '.lead-checkbox', function(e) {
                $('tr.lead-row').addClass('show-checkboxes');
                $('.lead-checkbox').show();
                updateButtons();
                e.stopPropagation();
            });
            
            // Update buttons when checkbox state changes
            $('#leads-tbody').on('change', '.lead-checkbox', function() {
                updateButtons();
            });
            
            // Select All button - FINAL FIX
            $(document).on('click', '#select-all-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();

                $('.lead-checkbox').show();
                $('tr.lead-row').addClass('show-checkboxes');
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');

                if (checkedVisibleCheckboxes.length === visibleCheckboxes.length && visibleCheckboxes.length > 0) {
                    // All are checked, so uncheck all
                    visibleCheckboxes.prop('checked', false).trigger('change');
                    $(this).text('{{__("Select All")}}');
                } else {
                    // Not all are checked, so check all
                    visibleCheckboxes.prop('checked', true).trigger('change');
                    $(this).text('{{__("Deselect All")}}');
                }
                updateButtons();
            });
            
            // Simple button update function
            function updateButtons() {
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedCheckboxes = $('.lead-checkbox:checked');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');
                
                console.log('Update buttons - Visible:', visibleCheckboxes.length, 'Checked:', checkedCheckboxes.length);
                
                // Show Select All button if any checkboxes are visible
                if (visibleCheckboxes.length > 0) {
                    $('#select-all-btn').show();
                    
                    // Update Select All button text
                    if (checkedVisibleCheckboxes.length === visibleCheckboxes.length && visibleCheckboxes.length > 0) {
                        $('#select-all-btn').text('{{__("Deselect All")}}');
                    } else {
                        $('#select-all-btn').text('{{__("Select All")}}');
                    }
                } else {
                    $('#select-all-btn').hide();
                }
                
                // Show Bulk Delete button if any checkboxes are checked
                if (checkedCheckboxes.length > 0) {
                    $('#bulk-delete-btn').show();
                    $('#bulk-delete-btn').text('{{__("Bulk Delete")}} (' + checkedCheckboxes.length + ')');
                } else {
                    $('#bulk-delete-btn').hide();
                }
            }
            
            // Hide everything when clicking outside (only if no checkboxes are visible)
            $(document).on('click', function(e) {
                // Only hide if clicking outside and NO checkboxes are visible
                if (
                    !$(e.target).closest('.lead-checkbox, #select-all-btn, #bulk-delete-btn').length &&
                    $('.lead-checkbox:visible').length === 0
                ) {
                    $('tr.lead-row').removeClass('show-checkboxes');
                    $('.lead-checkbox').hide();
                    $('#select-all-btn').hide();
                    $('#bulk-delete-btn').hide();
                }
            });
            
            // Bulk delete button click
            $('#bulk-delete-btn').on('click', function() {
                if ($('.lead-checkbox:checked').length === 0) return;
                
                var modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
                $('#confirmBulkDeleteBtn').prop('disabled', false);
                modal.show();
            });
            
            // Confirm bulk delete
            $('#confirmBulkDeleteBtn').on('click', function() {
                if (bulkDeleteInProgress) return;
                
                bulkDeleteInProgress = true;
                $(this).prop('disabled', true);
                
                var selected = $('.lead-checkbox:checked').map(function(){ 
                    return $(this).val(); 
                }).get();
                
                $.ajax({
                    url: '{{ route('leads.bulkDelete') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        lead_ids: selected
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        bulkDeleteInProgress = false;
                        $('#confirmBulkDeleteBtn').prop('disabled', false);
                        console.error('Bulk delete failed:', xhr);
                        alert('Bulk delete failed. Please try again.');
                    }
                });
            });

            // Debug button to log all checkboxes
            $('#debug-select-all-btn').on('click', function() {
                var allCheckboxes = $('.lead-checkbox');
                var checkedCheckboxes = $('.lead-checkbox:checked');
                var visibleCheckboxes = $('.lead-checkbox:visible');
                var checkedVisibleCheckboxes = $('.lead-checkbox:visible:checked');
                console.log('ALL checkboxes:', allCheckboxes.length, allCheckboxes);
                console.log('Checked checkboxes:', checkedCheckboxes.length, checkedCheckboxes);
                console.log('Visible checkboxes:', visibleCheckboxes.length, visibleCheckboxes);
                console.log('Checked visible checkboxes:', checkedVisibleCheckboxes.length, checkedVisibleCheckboxes);
            });

            $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });
        
        // Search functionality for leads
        $('#lead-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            // Show/hide clear icon based on input value
            if (searchTerm.length > 0) {
                $('.search-icon').hide();
                $('.clear-icon').show();
            } else {
                $('.search-icon').show();
                $('.clear-icon').hide();
            }
            
            var visibleRowsCount = 0;

            // Search through all table rows
            $('#leads-tbody tr').each(function() {
                var row = $(this);
                var leadName = row.find('.lead-name a').text().toLowerCase();
                var leadEmail = row.find('.lead-email').text().toLowerCase();
                var leadPhone = row.find('td:nth-child(8)').text().toLowerCase(); // Adjust if phone is in a different column

                // Check if search term matches any lead data
                var isMatch = leadName.includes(searchTerm) ||
                              leadEmail.includes(searchTerm) ||
                              leadPhone.includes(searchTerm);
                
                if (searchTerm === '' || isMatch) {
                    row.show();
                    visibleRowsCount++;
                } else {
                    row.hide();
                }
            });

            // Show message if no results found
            if (visibleRowsCount === 0 && searchTerm.length > 0) {
                if ($('#no-results-row').length === 0) {
                    $('#leads-tbody').append('<tr id="no-results-row"><td colspan="9" class="text-center py-4">{{__("No leads found matching the search criteria.")}}</td></tr>');
                }
            } else {
                $('#no-results-row').remove();
            }
        });
        
            // Search icon click handler
            $('.search-icon').on('click', function() {
                $('#lead-search').focus();
            });
        
            // Clear icon click handler
            $('.clear-icon').on('click', function() {
                $('#lead-search').val('').trigger('input');
                $('#lead-search').focus();
            });
        });

        // Filter functionality - wrapped in document ready
        $(document).ready(function() {
            console.log('Filter script loaded'); // Debug log
            
            let allUsers = [];
            let allTags = [];
            let allSources = [];
            let activeFilters = {
            assigned_to: [],
            tags: [],
            created_on: {
                range: '',
                start_date: '',
                end_date: ''
            },
            updated_on: {
                range: '',
                start_date: '',
                end_date: ''
            },
            sources: []
        };

        // Load users, tags, and sources when page loads
        loadUsers();
        loadTags();
        loadSources();

        // Open filter panel
        $('#openFilterPanel').on('click', function() {
            console.log('Filter button clicked'); // Debug log
            $('#filterOverlay').fadeIn(300);
            $('#filterPanel').addClass('active');
            showMainFilterView();
        });

        // Close filter panel
        $('#closeFilterPanel, #closeFilterPanel2, #closeFilterPanel3, #closeFilterPanel4, #closeFilterPanel5, #closeFilterPanel6, #filterOverlay, #cancelFilter, #cancelTagFilter, #cancelCreatedDateFilter, #cancelUpdatedDateFilter, #cancelSourceFilter, #cancelMainFilter').on('click', function() {
            closeFilterPanel();
        });

        // Navigate to Assigned To filter
        $('#assignedToCategory').on('click', function() {
            showAssignedToView();
        });

        // Navigate to Tag filter
        $('#filterByTagCategory').on('click', function() {
            showTagFilterView();
        });

        // Navigate to Created On filter
        $('#createdOnCategory').on('click', function() {
            showCreatedOnView();
        });

        // Navigate to Updated On filter
        $('#updatedOnCategory').on('click', function() {
            showUpdatedOnView();
        });

        // Navigate to Lead Source filter
        $('#leadSourceCategory').on('click', function() {
            showLeadSourceView();
        });

        // Back to main filter view
        $('#backToMainFilter, #backToMainFilterFromTag, #backToMainFilterFromCreatedDate, #backToMainFilterFromUpdatedDate, #backToMainFilterFromSource').on('click', function() {
            showMainFilterView();
        });

        // Search users
        $('#userSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderUserList(searchTerm);
        });

        // Search tags
        $('#tagSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderTagList(searchTerm);
        });

        // Search sources
        $('#sourceSearch').on('input', function() {
            const searchTerm = $(this).val();
            renderSourceList(searchTerm);
        });

        // Clear all filters
        $('#clearAllFilters').on('click', function() {
            activeFilters.assigned_to = [];
            renderUserList();
            updateFilterIndicator();
        });

        // Clear all tag filters
        $('#clearAllTagFilters').on('click', function() {
            activeFilters.tags = [];
            renderTagList();
            updateFilterIndicator();
        });

        // Clear created date filter
        $('#clearCreatedDateFilter').on('click', function() {
            activeFilters.created_on = { range: '', start_date: '', end_date: '' };
            $('input[name="dateRange"]').prop('checked', false);
            $('#customCreatedDateRange').hide();
            $('#createdStartDate, #createdEndDate').val('');
            updateFilterIndicator();
        });

        // Clear updated date filter
        $('#clearUpdatedDateFilter').on('click', function() {
            activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
            $('input[name="updatedDateRange"]').prop('checked', false);
            $('#customUpdatedDateRange').hide();
            $('#updatedStartDate, #updatedEndDate').val('');
            updateFilterIndicator();
        });

        // Clear source filter
        $('#clearAllSourceFilters').on('click', function() {
            activeFilters.sources = [];
            renderSourceList();
            updateFilterIndicator();
        });

        // Clear all filters from main view
        $('#clearAllMainFilters').on('click', function() {
            // Reset all active filters
            activeFilters.assigned_to = [];
            activeFilters.tags = [];
            activeFilters.created_on = { range: '', start_date: '', end_date: '' };
            activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
            activeFilters.sources = [];
            
            // Clear all form inputs
            $('#userList input[type="checkbox"]').prop('checked', false);
            $('#tagList input[type="checkbox"]').prop('checked', false);
            $('#sourceList input[type="checkbox"]').prop('checked', false);
            $('input[name="dateRange"]').prop('checked', false);
            $('input[name="updatedDateRange"]').prop('checked', false);
            $('#customCreatedDateRange').hide();
            $('#customUpdatedDateRange').hide();
            $('#createdStartDate, #createdEndDate, #updatedStartDate, #updatedEndDate').val('');
            
            // Update filter indicator and apply changes
            updateFilterIndicator();
            applyFiltersToTable();
            closeFilterPanel();
        });

        // Handle custom created date range toggle
        $('input[name="dateRange"]').on('change', function() {
            if ($(this).val() === 'custom') {
                $('#customCreatedDateRange').show();
            } else {
                $('#customCreatedDateRange').hide();
                $('#createdStartDate, #createdEndDate').val('');
            }
        });

        // Handle custom updated date range toggle
        $('input[name="updatedDateRange"]').on('change', function() {
            if ($(this).val() === 'custom') {
                $('#customUpdatedDateRange').show();
            } else {
                $('#customUpdatedDateRange').hide();
                $('#updatedStartDate, #updatedEndDate').val('');
            }
        });

        // Apply filter
        $('#applyFilter').on('click', function() {
            // Collect selected users
            const selectedUsers = [];
            $('#userList input[type="checkbox"]:checked').each(function() {
                selectedUsers.push($(this).val());
            });

            activeFilters.assigned_to = selectedUsers;
            updateFilterIndicator();
            applyFiltersToTable();
            closeFilterPanel();
        });

        // Apply tag filter
        $('#applyTagFilter').on('click', function() {
            // Collect selected tags
            const selectedTags = [];
            $('#tagList input[type="checkbox"]:checked').each(function() {
                selectedTags.push($(this).val());
            });

            activeFilters.tags = selectedTags;
            updateFilterIndicator();
            applyFiltersToTable();
            closeFilterPanel();
        });

        // Apply created date filter
        $('#applyCreatedDateFilter').on('click', function() {
            const selectedRange = $('input[name="dateRange"]:checked').val();
            
            if (selectedRange) {
                activeFilters.created_on.range = selectedRange;
                
                if (selectedRange === 'custom') {
                    const startDate = $('#createdStartDate').val();
                    const endDate = $('#createdEndDate').val();
                    
                    if (!startDate || !endDate) {
                        show_toastr('error', 'Please select both start and end dates for custom range.', 'error');
                        return;
                    }
                    
                    activeFilters.created_on.start_date = startDate;
                    activeFilters.created_on.end_date = endDate;
                } else {
                    activeFilters.created_on.start_date = '';
                    activeFilters.created_on.end_date = '';
                }
                
                updateFilterIndicator();
                applyFiltersToTable();
                closeFilterPanel();
            } else {
                // If no range selected, clear the filter
                activeFilters.created_on = { range: '', start_date: '', end_date: '' };
                updateFilterIndicator();
                applyFiltersToTable();
                closeFilterPanel();
            }
        });

        // Apply updated date filter
        $('#applyUpdatedDateFilter').on('click', function() {
            const selectedRange = $('input[name="updatedDateRange"]:checked').val();
            
            if (selectedRange) {
                activeFilters.updated_on.range = selectedRange;
                
                if (selectedRange === 'custom') {
                    const startDate = $('#updatedStartDate').val();
                    const endDate = $('#updatedEndDate').val();
                    
                    if (!startDate || !endDate) {
                        show_toastr('error', 'Please select both start and end dates for custom range.', 'error');
                        return;
                    }
                    
                    activeFilters.updated_on.start_date = startDate;
                    activeFilters.updated_on.end_date = endDate;
                } else {
                    activeFilters.updated_on.start_date = '';
                    activeFilters.updated_on.end_date = '';
                }
                
                updateFilterIndicator();
                applyFiltersToTable();
                closeFilterPanel();
            } else {
                // If no range selected, clear the filter
                activeFilters.updated_on = { range: '', start_date: '', end_date: '' };
                updateFilterIndicator();
                applyFiltersToTable();
                closeFilterPanel();
            }
        });

        // Apply source filter
        $('#applySourceFilter').on('click', function() {
            // Collect selected sources
            const selectedSources = [];
            $('#sourceList input[type="checkbox"]:checked').each(function() {
                selectedSources.push($(this).val());
            });

            activeFilters.sources = selectedSources;
            updateFilterIndicator();
            applyFiltersToTable();
            closeFilterPanel();
        });

        // Update filter indicator
        function updateFilterIndicator() {
            const hasFilters = activeFilters.assigned_to.length > 0 || 
                              activeFilters.tags.length > 0 || 
                              activeFilters.created_on.range !== '' ||
                              activeFilters.updated_on.range !== '' ||
                              activeFilters.sources.length > 0;
            $('#openFilterPanel').toggleClass('has-filters', hasFilters);
            
            if (hasFilters) {
                if ($('#openFilterPanel .filter-applied-indicator').length === 0) {
                    $('#openFilterPanel').css('position', 'relative').append('<span class="filter-applied-indicator"></span>');
                }
            } else {
                $('#openFilterPanel .filter-applied-indicator').remove();
            }
        }

        // View management functions
        function showMainFilterView() {
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterMainView').show();
        }

        function showAssignedToView() {
            $('#filterMainView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterAssignedToView').show();
        }

        function showTagFilterView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#filterByTagView').show();
        }

        function showCreatedOnView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').hide();
            $('#createdOnView').show();
        }

        function showUpdatedOnView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#leadSourceView').hide();
            $('#updatedOnView').show();
        }

        function showLeadSourceView() {
            $('#filterMainView').hide();
            $('#filterAssignedToView').hide();
            $('#filterByTagView').hide();
            $('#createdOnView').hide();
            $('#updatedOnView').hide();
            $('#leadSourceView').show();
        }

        function closeFilterPanel() {
            $('#filterOverlay').fadeOut(300);
            $('#filterPanel').removeClass('active');
        }

        // Load users for filter
        function loadUsers() {
            $.ajax({
                url: '{{ route('leads.filter.users') }}',
                type: 'GET',
                success: function(response) {
                    allUsers = response.users;
                    renderUserList();
                },
                error: function(xhr) {
                    console.error('Failed to load users:', xhr);
                }
            });
        }

        // Load tags for filter
        function loadTags() {
            $.ajax({
                url: '{{ route('leads.filter.tags') }}',
                type: 'GET',
                success: function(response) {
                    allTags = response.tags;
                    renderTagList();
                },
                error: function(xhr) {
                    console.error('Failed to load tags:', xhr);
                }
            });
        }

        // Load sources for filter
        function loadSources() {
            $.ajax({
                url: '{{ route('leads.filter.sources') }}',
                type: 'GET',
                success: function(response) {
                    allSources = response.sources;
                    renderSourceList();
                },
                error: function(xhr) {
                    console.error('Failed to load sources:', xhr);
                }
            });
        }

        // Render user list
        function renderUserList(searchTerm = '') {
            const userList = $('#userList');
            userList.empty();

            // Add "Assigned to None" option
            const noneItem = $(`
                <div class="user-item">
                    <input type="checkbox" value="none" id="user_none" ${activeFilters.assigned_to.includes('none') ? 'checked' : ''}>
                    <label for="user_none">{{ __('Assigned to None') }}</label>
                </div>
            `);
            userList.append(noneItem);

            // Filter and add users
            const filteredUsers = allUsers.filter(user => 
                user.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredUsers.forEach(user => {
                const userItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${user.id}" id="user_${user.id}" ${activeFilters.assigned_to.includes(user.id.toString()) ? 'checked' : ''}>
                        <label for="user_${user.id}">${user.name}</label>
                    </div>
                `);
                userList.append(userItem);
            });
        }

        // Render tag list
        function renderTagList(searchTerm = '') {
            const tagList = $('#tagList');
            tagList.empty();

            // Filter and add tags
            const filteredTags = allTags.filter(tag => 
                tag.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredTags.forEach(tag => {
                const tagItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${tag.id}" id="tag_${tag.id}" ${activeFilters.tags.includes(tag.id.toString()) ? 'checked' : ''}>
                        <label for="tag_${tag.id}">${tag.name}</label>
                    </div>
                `);
                tagList.append(tagItem);
            });
        }

        // Render source list
        function renderSourceList(searchTerm = '') {
            const sourceList = $('#sourceList');
            sourceList.empty();

            // Filter and add sources
            const filteredSources = allSources.filter(source => 
                source.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            filteredSources.forEach(source => {
                const sourceItem = $(`
                    <div class="user-item">
                        <input type="checkbox" value="${source.id}" id="source_${source.id}" ${activeFilters.sources.includes(source.id.toString()) ? 'checked' : ''}>
                        <label for="source_${source.id}">${source.name}</label>
                    </div>
                `);
                sourceList.append(sourceItem);
            });
        }

        // Apply filters to table
        function applyFiltersToTable() {
            if (activeFilters.assigned_to.length === 0 && 
                activeFilters.tags.length === 0 && 
                activeFilters.created_on.range === '' &&
                activeFilters.updated_on.range === '' &&
                activeFilters.sources.length === 0) {
                // Show all leads if no filters
                $('#leads-tbody tr').show();
                return;
            }

            $.ajax({
                url: '{{ route('leads.filter') }}',
                type: 'GET',
                data: {
                    assigned_to: activeFilters.assigned_to,
                    tags: activeFilters.tags,
                    created_on_range: activeFilters.created_on.range,
                    created_on_start: activeFilters.created_on.start_date,
                    created_on_end: activeFilters.created_on.end_date,
                    updated_on_range: activeFilters.updated_on.range,
                    updated_on_start: activeFilters.updated_on.start_date,
                    updated_on_end: activeFilters.updated_on.end_date,
                    sources: activeFilters.sources
                },
                success: function(response) {
                    // Hide all leads first
                    $('#leads-tbody tr').hide();
                    
                    // Show only filtered leads
                    response.leads.forEach(lead => {
                        $(`#leads-tbody tr[data-lead-id="${lead.id}"]`).show();
                    });
                },
                error: function(xhr) {
                    console.error('Filter failed:', xhr);
                    show_toastr('error', 'Filter failed. Please try again.', 'error');
                }
            });
        }
        }); // End document ready
    </script>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('Lead')}}</li>
@endsection
@section('action-btn')
    <div class="d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-md-end gap-3">
        <!-- Search Bar -->
        <div class="search-container mb-3 mb-md-0">
            <input type="text" 
                   id="lead-search" 
                   class="form-control search-input" 
                   placeholder="{{ __('Search leads...') }}" 
                   autocomplete="off">
            <i class="fas fa-search search-icon"></i>
            <i class="fas fa-times clear-icon" style="display: none;"></i>
        </div>
        
        {{ Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'mb-3 mb-md-0']) }}
        {{ Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['id' => 'default_pipeline_id', 'class' => 'form-control select']) }}
        {{ Form::close() }}
        <!-- Action Buttons Row -->
        <div class="action-buttons-row">
            <!-- List View -->
            <a href="{{ route('leads.list') }}"
                data-size="lg"
                data-ajax-popup="true"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="{{ __('List View') }}"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="ti ti-list" style="font-size:16px;"></i>
            </a>

            <!-- Import Lead -->
            <a href="#"
                data-size="md"
                data-bs-toggle="tooltip"
                data-ajax-popup="true"
                data-bs-placement="bottom"
                title="{{ __('Import') }}"
                data-url="{{ route('leads.import') }}"
                data-ajax-popup="true"
                data-title="{{ __('Import Lead CSV file') }}"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-file-import" style="font-size:16px;"></i>
            </a>

            <!-- Filter Button -->
            <button id="openFilterPanel"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="{{ __('Filter') }}"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;border:none;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-filter" style="font-size:16px;"></i>
            </button>

            <!-- Export Leads -->
            <a href="{{ route('leads.export') }}"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="{{ __('Export') }}"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="fas fa-file-export" style="font-size:16px;"></i>
            </a>

            <!-- Create New Lead -->
            <a href="#"
                data-size="lg"
                data-url="{{ route('leads.create') }}"
                data-ajax-popup="true"
                data-bs-toggle="tooltip"
                data-bs-placement="bottom"
                title="{{ __('Create New Lead') }}"
                data-title="{{ __('Create Lead') }}"
                class="action-btn"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                        border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                        box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)'"
                onmouseout="this.style.transform='scale(1)'">
                <i class="ti ti-plus" style="font-size:16px;"></i>
            </a>
        </div>
    </div>
@endsection

@section('content')
    <!-- Filter Panel -->
    <div class="filter-overlay" id="filterOverlay"></div>
    <div id="filterPanel" class="filter-panel">
        <!-- Main Filter Categories View -->
        <div id="filterMainView" class="filter-view">
            <div class="filter-header">
                <div class="header-left">
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Filters') }}</h4>
                </div>
                <button id="closeFilterPanel" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-categories">
                    <div class="filter-category-item" id="assignedToCategory">
                        <div class="category-left">
                            <i class="fas fa-user-friends category-icon"></i>
                            <span class="category-name">{{ __('Assigned to') }}</span>
                        </div>
                        <i class="fas fa-chevron-right category-arrow"></i>
                    </div>
                    <div class="filter-category-item" id="filterByTagCategory">
                        <div class="category-left">
                            <i class="fas fa-tags category-icon"></i>
                            <span class="category-name">{{ __('Filter By Tag') }}</span>
                        </div>
                        <i class="fas fa-chevron-right category-arrow"></i>
                    </div>
                    <div class="filter-category-item" id="createdOnCategory">
                        <div class="category-left">
                            <i class="fas fa-calendar-alt category-icon"></i>
                            <span class="category-name">{{ __('Created on') }}</span>
                        </div>
                        <i class="fas fa-chevron-right category-arrow"></i>
                    </div>
                    <div class="filter-category-item" id="updatedOnCategory">
                        <div class="category-left">
                            <i class="fas fa-calendar-check category-icon"></i>
                            <span class="category-name">{{ __('Updated on') }}</span>
                        </div>
                        <i class="fas fa-chevron-right category-arrow"></i>
                    </div>
                    <div class="filter-category-item" id="leadSourceCategory">
                        <div class="category-left">
                            <i class="fas fa-map-marker-alt category-icon"></i>
                            <span class="category-name">{{ __('Lead source') }}</span>
                        </div>
                        <i class="fas fa-chevron-right category-arrow"></i>
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearAllMainFilters" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelMainFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                </div>
            </div>
        </div>

        <!-- Assigned To Detail View -->
        <div id="filterAssignedToView" class="filter-view" style="display: none;">
            <div class="filter-header">
                <div class="header-left">
                    <button id="backToMainFilter" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Assigned to') }}</h4>
                </div>
                <button id="closeFilterPanel2" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <input type="text" id="userSearch" placeholder="{{ __('Search') }}" class="user-search" />
                    <div id="userList" class="user-list">
                        <!-- Users will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearAllFilters" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                    <button id="applyFilter" class="btn btn-success">{{ __('Apply') }}</button>
                </div>
            </div>
        </div>

        <!-- Tag Filter Detail View -->
        <div id="filterByTagView" class="filter-view" style="display: none;">
            <div class="filter-header">
                <div class="header-left">
                    <button id="backToMainFilterFromTag" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Filter By Tag') }}</h4>
                </div>
                <button id="closeFilterPanel3" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <input type="text" id="tagSearch" placeholder="{{ __('Search') }}" class="user-search" />
                    <div id="tagList" class="user-list">
                        <!-- Tags will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearAllTagFilters" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelTagFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                    <button id="applyTagFilter" class="btn btn-success">{{ __('Apply') }}</button>
                </div>
            </div>
        </div>

        <!-- Created On Detail View -->
        <div id="createdOnView" class="filter-view" style="display: none;">
            <div class="filter-header">
                <div class="header-left">
                    <button id="backToMainFilterFromCreatedDate" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Created on') }}</h4>
                </div>
                <button id="closeFilterPanel4" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <div id="createdDateRangeList" class="user-list">
                        <div class="user-item date-option" data-range="today">
                            <input type="radio" name="dateRange" value="today" id="date_today">
                            <label for="date_today">{{ __('Today') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="yesterday">
                            <input type="radio" name="dateRange" value="yesterday" id="date_yesterday">
                            <label for="date_yesterday">{{ __('Yesterday') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_week">
                            <input type="radio" name="dateRange" value="this_week" id="date_this_week">
                            <label for="date_this_week">{{ __('This Week') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_week">
                            <input type="radio" name="dateRange" value="last_week" id="date_last_week">
                            <label for="date_last_week">{{ __('Last Week') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_7_days">
                            <input type="radio" name="dateRange" value="last_7_days" id="date_last_7_days">
                            <label for="date_last_7_days">{{ __('Last 7 Days') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_30_days">
                            <input type="radio" name="dateRange" value="last_30_days" id="date_last_30_days">
                            <label for="date_last_30_days">{{ __('Last 30 Days') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_month">
                            <input type="radio" name="dateRange" value="this_month" id="date_this_month">
                            <label for="date_this_month">{{ __('This Month') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_month">
                            <input type="radio" name="dateRange" value="last_month" id="date_last_month">
                            <label for="date_last_month">{{ __('Last Month') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_year">
                            <input type="radio" name="dateRange" value="this_year" id="date_this_year">
                            <label for="date_this_year">{{ __('This Year') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_year">
                            <input type="radio" name="dateRange" value="last_year" id="date_last_year">
                            <label for="date_last_year">{{ __('Last Year') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="custom">
                            <input type="radio" name="dateRange" value="custom" id="date_custom">
                            <label for="date_custom">{{ __('Custom') }}</label>
                        </div>
                    </div>
                    
                    <!-- Custom Date Range Picker -->
                    <div id="customCreatedDateRange" style="display: none; margin-top: 16px;">
                        <div class="date-range-picker">
                            <input type="date" id="createdStartDate" class="form-control date-input" placeholder="{{ __('Start Date') }}">
                            <span class="date-separator">-</span>
                            <input type="date" id="createdEndDate" class="form-control date-input" placeholder="{{ __('End Date') }}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearCreatedDateFilter" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelCreatedDateFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                    <button id="applyCreatedDateFilter" class="btn btn-success">{{ __('Apply') }}</button>
                </div>
            </div>
        </div>

        <!-- Updated On Detail View -->
        <div id="updatedOnView" class="filter-view" style="display: none;">
            <div class="filter-header">
                <div class="header-left">
                    <button id="backToMainFilterFromUpdatedDate" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Updated on') }}</h4>
                </div>
                <button id="closeFilterPanel5" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <div id="updatedDateRangeList" class="user-list">
                        <div class="user-item date-option" data-range="today">
                            <input type="radio" name="updatedDateRange" value="today" id="updated_date_today">
                            <label for="updated_date_today">{{ __('Today') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="yesterday">
                            <input type="radio" name="updatedDateRange" value="yesterday" id="updated_date_yesterday">
                            <label for="updated_date_yesterday">{{ __('Yesterday') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_week">
                            <input type="radio" name="updatedDateRange" value="this_week" id="updated_date_this_week">
                            <label for="updated_date_this_week">{{ __('This Week') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_week">
                            <input type="radio" name="updatedDateRange" value="last_week" id="updated_date_last_week">
                            <label for="updated_date_last_week">{{ __('Last Week') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_7_days">
                            <input type="radio" name="updatedDateRange" value="last_7_days" id="updated_date_last_7_days">
                            <label for="updated_date_last_7_days">{{ __('Last 7 Days') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_30_days">
                            <input type="radio" name="updatedDateRange" value="last_30_days" id="updated_date_last_30_days">
                            <label for="updated_date_last_30_days">{{ __('Last 30 Days') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_month">
                            <input type="radio" name="updatedDateRange" value="this_month" id="updated_date_this_month">
                            <label for="updated_date_this_month">{{ __('This Month') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_month">
                            <input type="radio" name="updatedDateRange" value="last_month" id="updated_date_last_month">
                            <label for="updated_date_last_month">{{ __('Last Month') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="this_year">
                            <input type="radio" name="updatedDateRange" value="this_year" id="updated_date_this_year">
                            <label for="updated_date_this_year">{{ __('This Year') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="last_year">
                            <input type="radio" name="updatedDateRange" value="last_year" id="updated_date_last_year">
                            <label for="updated_date_last_year">{{ __('Last Year') }}</label>
                        </div>
                        <div class="user-item date-option" data-range="custom">
                            <input type="radio" name="updatedDateRange" value="custom" id="updated_date_custom">
                            <label for="updated_date_custom">{{ __('Custom') }}</label>
                        </div>
                    </div>
                    
                    <!-- Custom Date Range Picker -->
                    <div id="customUpdatedDateRange" style="display: none; margin-top: 16px;">
                        <div class="date-range-picker">
                            <input type="date" id="updatedStartDate" class="form-control date-input" placeholder="{{ __('Start Date') }}">
                            <span class="date-separator">-</span>
                            <input type="date" id="updatedEndDate" class="form-control date-input" placeholder="{{ __('End Date') }}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearUpdatedDateFilter" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelUpdatedDateFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                    <button id="applyUpdatedDateFilter" class="btn btn-success">{{ __('Apply') }}</button>
                </div>
            </div>
        </div>

        <!-- Lead Source Detail View -->
        <div id="leadSourceView" class="filter-view" style="display: none;">
            <div class="filter-header">
                <div class="header-left">
                    <button id="backToMainFilterFromSource" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <i class="fas fa-filter filter-icon"></i>
                    <h4>{{ __('Lead source') }}</h4>
                </div>
                <button id="closeFilterPanel6" class="close-btn">&times;</button>
            </div>
            <div class="filter-content">
                <div class="filter-section">
                    <input type="text" id="sourceSearch" placeholder="{{ __('Search') }}" class="user-search" />
                    <div id="sourceList" class="user-list">
                        <!-- Sources will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="filter-footer">
                <button id="clearAllSourceFilters" class="btn btn-link">{{ __('Clear all') }}</button>
                <div>
                    <button id="cancelSourceFilter" class="btn btn-secondary me-2">{{ __('Cancel') }}</button>
                    <button id="applySourceFilter" class="btn btn-success">{{ __('Apply') }}</button>
                </div>
            </div>
        </div>
    </div>

    @if($pipeline)
        <div class="row">
            <div class="col-xl-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">{{__('Leads List')}}</h5>
                            </div>
                            <div class="col-auto">
                                <button id="select-all-btn" class="btn btn-info btn-sm me-2" style="display: none;">{{__('Select All')}}</button>
                                <button id="bulk-delete-btn" class="btn btn-danger btn-sm" style="display: none;">{{__('Bulk Delete')}}</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable" id="leads-table">
                                <thead>
                                <tr>
                                    <th></th> <!-- Checkbox column -->
                                    <th>{{__('Source')}}</th>
                                    <th>{{__('Name')}}</th>
                                    <th>{{__('Email')}}</th>
                                    <!-- <th>{{__('Subject')}}</th> -->
                                    <th>{{__('Stage')}}</th>
                                    <th>{{__('Assigned To')}}</th>
                                    <th>{{__('Follow-Up Date')}}</th>
                                    <th>{{__('Contact')}}</th>
                                    <th>{{__('Action')}}</th>
                                </tr>
                                </thead>
                                <tbody id="leads-tbody">
                                @if(count($leads) > 0)
                                    @foreach ($leads as $lead)
                                        @php
                                            $today = \Carbon\Carbon::today();
                                            $followUp = $lead->next_follow_up_date ? \Carbon\Carbon::parse($lead->next_follow_up_date) : null;
                                            $highlight = '';
                                            if ($followUp) {
                                                if ($followUp->isToday()) {
                                                    $highlight = 'badge bg-warning text-dark';
                                                } elseif ($followUp->isPast()) {
                                                    $highlight = 'badge bg-danger';
                                                }
                                            }
                                        @endphp
                                        <tr class="internal-lead lead-row" data-source="internal" data-lead-id="{{ $lead->id }}">
                                            <td>
                                                <input type="checkbox" class="lead-checkbox" value="{{$lead->id}}" />
                                            </td>
                                            <td>
                                                <span class="data-source-badge source-internal">
                                                    <i class="ti ti-database"></i> {{__('Internal')}}
                                                </span>
                                            </td>
                                            <td class="lead-name">
                                                <a href="{{ route('leads.show', $lead->id) }}" class="text-primary text-decoration-underline" style="cursor:pointer;">
                                                    {{ $lead->name }}
                                                </a>
                                            </td>
                                            <td class="lead-email">{{ $lead->email }}</td>
                                            <!-- <td>{{ $lead->subject }}</td> -->
                                            <td>{{  !empty($lead->stage)?$lead->stage->name:'-' }}</td>
                                            <td>
                                                @foreach($lead->users as $user)
                                                <div class="d-flex align-items-center">
                                                        <a href="#"
                                                        class="btn btn-sm me-1 p-0 d-flex align-items-center justify-content-center rounded-circle"
                                                        style="width: 30px; height: 30px; background-color: #f0f0f0; transition: all 0.3s ease;"
                                                        data-bs-toggle="tooltip"
                                                        data-bs-placement="top"
                                                        title="{{ $user->name }}">
                                                            <i class="ti ti-user" style="font-size: 16px; color: #333;"></i>
                                                        </a>
                                                    @endforeach
                                                </div>
                                            </td>
                                            <td>
                                                @if($lead->next_follow_up_date)
                                                    <span class="{{ $highlight }}">
                                                        {{ \Carbon\Carbon::parse($lead->next_follow_up_date)->format('d-m-Y') }}
                                                    </span>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                <?php
                                                $sources = $lead->sources();
                                                ?>
                                                <div class="communication-buttons">
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                        style="
                                                            background: linear-gradient(135deg, #6f42c1, #065f46);
                                                            border: none;
                                                            border-radius: 8px;
                                                            padding: 8px 12px;
                                                            transition: all 0.3s ease;
                                                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                        "
                                                        onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                        data-bs-toggle="tooltip"
                                                        title="Call"
                                                        onclick="window.open('tel:{{ $lead->phone }}', '_self')">
                                                        <i class="fas fa-phone-alt text-white" style="font-size: 12px;"></i>
                                                    </button>

                                                    {{-- Whatsapp Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #1ebea5, #00e676);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="WhatsApp"
                                                            onclick="openWhatsApp('{{ $lead->phone }}')">
                                                        <i class="fab fa-whatsapp text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Email Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #0d6efd, #0b5ed7);
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-bs-toggle="tooltip"
                                                            title="Email"
                                                            onclick="openCommunicationModal('email')">
                                                        <i class="fas fa-envelope text-white" style="font-size: 16px;"></i>
                                                    </button>

                                                    {{-- Sources Button --}}
                                                    <button class="btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                border: 1px solid #0d6efd;
                                                                background-color: transparent;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                            "
                                                            onmouseover="this.style.backgroundColor='#0d6efd'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#0d6efd'; this.style.boxShadow='none'"
                                                            title="Activity"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#activityModal-{{ $lead->id }}">
                                                        <i class="fas fa-stream" style="font-size: 16px; color: #0d6efd;"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            @if(Auth::user()->type != 'client')
                                                <td class="Action">
                                                    <span>
                                                    @can('view lead')
                                                            @if($lead->is_active)
                                                            <!-- <div class="action-btn">
                                                                <a href="{{ route('leads.show', $lead->id) }}"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                        background: linear-gradient(135deg, #14532d, #065f46); 
                                                                        border: none;
                                                                        border-radius: 8px;
                                                                        padding: 8px 12px;
                                                                        transition: all 0.3s ease;
                                                                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                                "
                                                                onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                                data-size="xl"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('View') }}"
                                                                data-title="{{ __('Lead Detail') }}">
                                                                    <i class="ti ti-eye text-white" style="font-size: 16px;"></i>
                                                                </a>
                                                            </div> -->
                                                            @endif
                                                        @endcan
                                                        @can('edit lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            <a href="#"
                                                            class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                            style="
                                                                background: linear-gradient(135deg, #14532d, #065f46); 
                                                                border: none;
                                                                border-radius: 8px;
                                                                padding: 8px 12px;
                                                                transition: all 0.3s ease;
                                                                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                                            "
                                                            onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                            onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 6px rgba(0,0,0,0.1)'"
                                                            data-url="{{ route('leads.edit', $lead->id) }}"
                                                            data-ajax-popup="true"
                                                            data-size="xl"
                                                            data-bs-toggle="tooltip"
                                                            title="{{ __('Edit') }}"
                                                            data-title="{{ __('Lead Edit') }}">
                                                                <i class="ti ti-pencil text-white" style="font-size: 16px;"></i>
                                                            </a>
                                                        </div>
                                                        @endcan
                                                        @can('delete lead')
                                                        <div class="action-btn" style="margin-left: 15px;">
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['leads.destroy', $lead->id], 'id' => 'delete-form-' . $lead->id]) !!}
                                                                <a href="#"
                                                                onclick="event.preventDefault(); document.getElementById('delete-form-{{ $lead->id }}').submit();"
                                                                class="mx-3 btn btn-sm d-flex align-items-center justify-content-center"
                                                                style="
                                                                    border: 1px solid #dc3545;
                                                                    background-color: transparent;
                                                                    border-radius: 8px;
                                                                    padding: 8px 12px;
                                                                    transition: all 0.3s ease;
                                                                "
                                                                onmouseover="this.style.backgroundColor='#dc3545'; this.querySelector('i').style.color='#fff'; this.style.boxShadow='0 6px 10px rgba(0,0,0,0.15)'"
                                                                onmouseout="this.style.backgroundColor='transparent'; this.querySelector('i').style.color='#14532d'; this.style.boxShadow='none'"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}">
                                                                    <i class="ti ti-trash" style="font-size: 16px; color: #14532d;"></i>
                                                                </a>
                                                            {!! Form::close() !!}
                                                        </div>
                                                        @endcan
                                                    </span>
                                                </td>
                                            @endif
                                        </tr>
                                        <!-- Activity Modal for this lead -->
                                        <div class="modal fade" id="activityModal-{{ $lead->id }}" tabindex="-1" aria-labelledby="activityModalLabel-{{ $lead->id }}" aria-hidden="true">
                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="activityModalLabel-{{ $lead->id }}">{{ __('Activity') }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row leads-scroll">
                                                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                                                @if (!$lead->activities->isEmpty())
                                                                    @foreach ($lead->activities as $activity)
                                                                        <li class="list-group-item card mb-3">
                                                                            <div class="row align-items-center justify-content-between">
                                                                                <div class="col-auto mb-3 mb-sm-0">
                                                                                    <div class="d-flex align-items-center">
                                                                                        <div class="theme-avtar bg-primary badge">
                                                                                            <i class="ti {{ $activity->logIcon() }}"></i>
                                                                                        </div>
                                                                                        <div class="ms-3">
                                                                                            <span class="text-dark text-sm">{{ __($activity->log_type) }}</span>
                                                                                            <h6 class="m-0">{!! $activity->getLeadRemark() !!}</h6>
                                                                                            <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </li>
                                                                    @endforeach
                                                                @else
                                                                    <li class="text-center py-4">No activity found yet.</li>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <tr class="font-style no-data-row">
                                        <td colspan="7" class="text-center">{{ __('No data available in table') }}</td>
                                    </tr>
                                @endif

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Communication Modal -->
    <div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="communication-modal-label">Communication Options</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="communication-options-list">
                        <a href="#" class="communication-option-item" id="whatsapp-option">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                        <a href="#" class="communication-option-item" id="default-email-option">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>Default Email App</span>
                        </a>
                        <a href="#" class="communication-option-item" id="cloud-email-option">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Cloud Email Service</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sources Modal -->
    <div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sources-modal-label">{{ __('Lead Sources') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="list-group" id="sources-list">
                        <!-- Sources will be dynamically inserted here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkDeleteModalLabel">{{ __('Confirm Bulk Delete') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('Are you sure you want to delete the selected leads? This action cannot be undone.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('No') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn">{{ __('Yes') }}</button>
                </div>
            </div>
        </div>
    </div>



@endsection