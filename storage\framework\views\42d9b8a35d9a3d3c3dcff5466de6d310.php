    <?php
        use App\Models\Utility;
        $setting = \App\Models\Utility::settings();
        $logo = \App\Models\Utility::get_file('uploads/logo');

        $company_logo = $setting['company_logo_dark'] ?? '';
        $company_logos = $setting['company_logo_light'] ?? '';
        $company_small_logo = $setting['company_small_logo'] ?? '';

        $emailTemplate = \App\Models\EmailTemplate::emailTemplateData();
        $lang = Auth::user()->lang;

        $user = \Auth::user();
        $userPlan = null;
        if ($user->type === 'company') {
            // Load the pricing plan relationship or find by ID if relationship fails
            $pricingPlan = $user->plan;
            if (is_numeric($pricingPlan)) {
                // If plan returns an ID instead of model, fetch the model
                $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
            }
            $userPlan = $pricingPlan;
        } elseif ($user->type === 'employee') {
            // For employees, get the company's plan to check module availability
            $companyUser = \App\Models\User::find($user->created_by);
            if ($companyUser && $companyUser->plan) {
                $pricingPlan = $companyUser->plan;
                if (is_numeric($pricingPlan)) {
                    $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
                }
                $userPlan = $pricingPlan;
            }
        } else {
            $userPlan = \App\Models\Plan::getPlan($user->show_dashboard());
        }
    ?>

<!-- FontAwesome CDN for Icons - Backup if local fails -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<!-- Tabler Icons as additional fallback -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" />

<!-- Modern 3D Menu Icons CSS -->
<style>
    /* Reset any conflicting styles */
    .dash-micon,
    .dash-micon i,
    .dash-micon *,
    .dash-item .dash-micon,
    .dash-item .dash-micon i,
    .dash-item .dash-micon * {
        box-sizing: border-box;
    }

    /* 3D Icon Base Styles */
    .dash-micon {
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 50px !important;
        height: 50px !important;
        min-width: 50px !important;
        min-height: 50px !important;
        border-radius: 15px !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 50%, rgba(var(--theme-color-rgb), 0.6) 100%) !important;
        box-shadow:
            0 6px 20px rgba(var(--theme-color-rgb), 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.25),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        margin-right: 16px !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    .dash-micon::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%) !important;
        border-radius: inherit !important;
        transition: opacity 0.3s ease !important;
        pointer-events: none !important;
        z-index: 1 !important;
    }

    /* Icon Styles - Force FontAwesome to display */
    .dash-micon i,
    .dash-micon .fas,
    .dash-micon .fab,
    .dash-micon .far,
    .dash-micon .fal,
    .dash-micon .ti {
        color: #ffffff !important;
        font-size: 22px !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
        transition: all 0.3s ease !important;
        font-weight: 900 !important;
        line-height: 1 !important;
        display: block !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 5 Free", "FontAwesome" !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        flex-shrink: 0 !important;
    }

    /* Specific FontAwesome classes */
    .dash-micon .fas {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
    }

    .dash-micon .fab {
        font-family: "Font Awesome 6 Brands" !important;
        font-weight: 400 !important;
    }

    .dash-micon .far {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 400 !important;
    }

    /* SVG Icon Styles - Flexbox Centering */
    .dash-micon svg {
        width: 22px !important;
        height: 22px !important;
        fill: none !important;
        stroke: #ffffff !important;
        stroke-width: 2.5 !important;
        stroke-linecap: round !important;
        stroke-linejoin: round !important;
        display: block !important;
        flex-shrink: 0 !important;
    }

    /* Active State */
    .dash-item.active .dash-micon,
    .dash-item.dash-trigger .dash-micon,
    .dash-item.active > .dash-link .dash-micon,
    .dash-item.dash-trigger > .dash-link .dash-micon {
        transform: scale(1.12) !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.9) 50%, rgba(var(--theme-color-rgb), 0.7) 100%) !important;
        box-shadow:
            0 12px 35px rgba(var(--theme-color-rgb), 0.6),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
    }

    .dash-item.active .dash-micon i,
    .dash-item.dash-trigger .dash-micon i,
    .dash-item.active .dash-micon .fas,
    .dash-item.dash-trigger .dash-micon .fas,
    .dash-item.active .dash-micon .fab,
    .dash-item.dash-trigger .dash-micon .fab,
    .dash-item.active .dash-micon .ti,
    .dash-item.dash-trigger .dash-micon .ti,
    .dash-item.active > .dash-link .dash-micon i,
    .dash-item.dash-trigger > .dash-link .dash-micon i {
        transform: scale(1.1) !important;
        color: #ffffff !important;
        text-shadow: 0 3px 8px rgba(0, 0, 0, 0.6) !important;
        font-weight: 600 !important;
        font-size: 26px !important;
    }

    /* Hover Effects */
    .dash-item:hover .dash-micon,
    .dash-item:hover > .dash-link .dash-micon {
        transform: translateY(-4px) scale(1.06) !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.85) 50%, rgba(var(--theme-color-rgb), 0.65) 100%) !important;
        box-shadow:
            0 10px 30px rgba(var(--theme-color-rgb), 0.5),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.2) !important;
    }

    .dash-item:hover .dash-micon::before {
        opacity: 1 !important;
    }

    .dash-item:hover .dash-micon i,
    .dash-item:hover .dash-micon .fas,
    .dash-item:hover .dash-micon .fab,
    .dash-item:hover .dash-micon .ti,
    .dash-item:hover > .dash-link .dash-micon i {
        transform: scale(1.05) !important;
        color: #ffffff !important;
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5) !important;
        font-size: 25px !important;
    }

    /* Submenu Icons */
    .dash-submenu .dash-micon {
        width: 42px !important;
        height: 42px !important;
        min-width: 42px !important;
        min-height: 42px !important;
        background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.6) 0%, rgba(var(--theme-color-rgb), 0.4) 50%, rgba(var(--theme-color-rgb), 0.3) 100%) !important;
        box-shadow:
            0 4px 12px rgba(var(--theme-color-rgb), 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.2),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
        border-radius: 12px !important;
    }

    .dash-submenu .dash-micon i,
    .dash-submenu .dash-micon .fas,
    .dash-submenu .dash-micon .fab,
    .dash-submenu .dash-micon .ti {
        font-size: 20px !important;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    }

    .dash-submenu .dash-item.active .dash-micon {
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%) !important;
        transform: scale(1.1) !important;
        box-shadow:
            0 6px 18px rgba(var(--theme-color-rgb), 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2) !important;
    }

    .dash-submenu .dash-item.active .dash-micon i,
    .dash-submenu .dash-item.active .dash-micon .fas,
    .dash-submenu .dash-item.active .dash-micon .fab,
    .dash-submenu .dash-item.active .dash-micon .ti {
        color: #ffffff !important;
        font-size: 22px !important;
        font-weight: 600 !important;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .dash-micon {
            width: 46px !important;
            height: 46px !important;
            min-width: 46px !important;
            min-height: 46px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 22px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 38px !important;
            height: 38px !important;
            min-width: 38px !important;
            min-height: 38px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 18px !important;
            color: #ffffff !important;
        }
    }

    @media (max-width: 768px) {
        .dash-micon {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            min-height: 44px !important;
            margin-right: 12px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 20px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 36px !important;
            height: 36px !important;
            min-width: 36px !important;
            min-height: 36px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 16px !important;
            color: #ffffff !important;
        }
    }

    @media (max-width: 480px) {
        .dash-micon {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
            min-height: 40px !important;
            margin-right: 10px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 18px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 32px !important;
            height: 32px !important;
            min-width: 32px !important;
            min-height: 32px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 14px !important;
            color: #ffffff !important;
        }
    }

    /* Settings submenu responsive improvements */
    @media (max-width: 768px) {
        .dash-submenu .dash-mtext {
            font-size: 14px !important;
        }

        .dash-submenu .dash-link {
            padding: 10px 12px !important;
        }
    }

    @media (max-width: 480px) {
        .dash-submenu .dash-mtext {
            font-size: 13px !important;
        }

        .dash-submenu .dash-link {
            padding: 8px 10px !important;
        }

        /* Ensure settings menu items are easily tappable on mobile */
        .dash-submenu .dash-item {
            min-height: 44px !important;
        }
    }

    /* Animation for menu items */
    .dash-item {
        transition: all 0.3s ease;
    }

    .dash-link {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* .dash-item:hover .dash-link {
        background: rgba(var(--theme-color-rgb), 0.1);
    } */

    /* .dash-item.active .dash-link {
        background: rgba(var(--theme-color-rgb), 0.15);
    } */

    /* Menu text styling */
    .dash-mtext {
        font-weight: 500;
        color: #374151;
        transition: color 0.3s ease;
    }

    /* Top-level menu items with 3D icons - white text on active/hover */
    .dash-navbar > .dash-item.dash-hasmenu.active .dash-mtext {
        color: #ffffff;
        font-weight: 600;
    }

    .dash-navbar > .dash-item.dash-hasmenu:hover .dash-mtext {
        color: #ffffff;
    }

    /* Regular menu items - keep theme colors */
    .dash-navbar > .dash-item:not(.dash-hasmenu).active .dash-mtext {
        color: var(--theme-color);
        font-weight: 600;
    }

    .dash-navbar > .dash-item:not(.dash-hasmenu):hover .dash-mtext {
        color: var(--theme-color);
    }

    /* Submenu items - keep original colors */
    .dash-submenu .dash-item.active .dash-mtext {
        color: var(--theme-color);
        font-weight: 600;
    }

    .dash-submenu .dash-item:hover .dash-mtext {
        color: var(--theme-color);
    }

    /* Arrow styling */
    .dash-arrow {
        margin-left: auto;
        transition: transform 0.3s ease;
    }

    .dash-item.dash-trigger .dash-arrow {
        transform: rotate(90deg);
    }

    /* Force icon visibility and override any conflicting styles */
    .dash-navbar .dash-micon,
    .dash-submenu .dash-micon,
    .navbar-content .dash-micon {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .dash-navbar .dash-micon i,
    .dash-navbar .dash-micon .fas,
    .dash-navbar .dash-micon .fab,
    .dash-navbar .dash-micon .ti,
    .dash-submenu .dash-micon i,
    .dash-submenu .dash-micon .fas,
    .dash-submenu .dash-micon .fab,
    .dash-submenu .dash-micon .ti,
    .navbar-content .dash-micon i,
    .navbar-content .dash-micon .fas,
    .navbar-content .dash-micon .fab,
    .navbar-content .dash-micon .ti {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #ffffff !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", inherit !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
        -webkit-font-smoothing: antialiased !important;
    }

    /* Ensure FontAwesome icons are properly loaded */
    .fas, .fab, .far, .fal {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
    }

    .fab {
        font-family: "Font Awesome 6 Brands" !important;
        font-weight: 400 !important;
    }

    /* Additional icon fixes */
    .dash-link {
        display: flex !important;
        align-items: center !important;
        text-decoration: none !important;
    }

    .dash-mtext {
        flex: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* Hide FontAwesome icons when SVG is present */
    .dash-micon.has-svg i {
        display: none !important;
    }

    /* Fallback icon styles */
    .dash-micon .fallback-icon {
        font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
        font-size: 20px !important;
        line-height: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Theme color support for custom colors */
    body.custom-color .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.8) 50%, rgba(var(--color-customColor-rgb), 0.6) 100%) !important;
        box-shadow:
            0 6px 20px rgba(var(--color-customColor-rgb), 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.25),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
    }

    body.custom-color .dash-item.active .dash-micon,
    body.custom-color .dash-item.dash-trigger .dash-micon,
    body.custom-color .dash-item.active > .dash-link .dash-micon,
    body.custom-color .dash-item.dash-trigger > .dash-link .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.9) 50%, rgba(var(--color-customColor-rgb), 0.7) 100%) !important;
        box-shadow:
            0 12px 35px rgba(var(--color-customColor-rgb), 0.6),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
    }

    body.custom-color .dash-item:hover .dash-micon,
    body.custom-color .dash-item:hover > .dash-link .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.85) 50%, rgba(var(--color-customColor-rgb), 0.65) 100%) !important;
        box-shadow:
            0 10px 30px rgba(var(--color-customColor-rgb), 0.5),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.2) !important;
    }

    body.custom-color .dash-submenu .dash-micon {
        background: linear-gradient(135deg, rgba(var(--color-customColor-rgb), 0.6) 0%, rgba(var(--color-customColor-rgb), 0.4) 50%, rgba(var(--color-customColor-rgb), 0.3) 100%) !important;
        box-shadow:
            0 4px 12px rgba(var(--color-customColor-rgb), 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.2),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
    }

    body.custom-color .dash-submenu .dash-item.active .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.8) 100%) !important;
        box-shadow:
            0 6px 18px rgba(var(--color-customColor-rgb), 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2) !important;
    }

    body.custom-color .dash-item:hover .dash-link {
        background: rgba(var(--color-customColor-rgb), 0.1);
    }

    body.custom-color .dash-item.active .dash-link {
        background: rgba(var(--color-customColor-rgb), 0.15);
    }

    /* Custom color theme - top-level menu items with 3D icons */
    body.custom-color .dash-navbar > .dash-item.dash-hasmenu.active .dash-mtext {
        color: #ffffff;
    }

    body.custom-color .dash-navbar > .dash-item.dash-hasmenu:hover .dash-mtext {
        color: #ffffff;
    }

    /* Custom color theme - regular menu items */
    body.custom-color .dash-navbar > .dash-item:not(.dash-hasmenu).active .dash-mtext {
        color: var(--color-customColor);
    }

    body.custom-color .dash-navbar > .dash-item:not(.dash-hasmenu):hover .dash-mtext {
        color: var(--color-customColor);
    }

    /* Custom color theme - submenu items */
    body.custom-color .dash-submenu .dash-item.active .dash-mtext {
        color: var(--color-customColor);
    }

    body.custom-color .dash-submenu .dash-item:hover .dash-mtext {
        color: var(--color-customColor);
    }
</style>

<!-- JavaScript to ensure icons display properly -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // SVG Icon definitions
    const svgIcons = {
        'fa-home': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>',
        'fa-chart-pie': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"/><path d="m22 12-10-10v10z"/></svg>',
        'fa-calculator': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="2" width="16" height="20" rx="2"/><line x1="8" y1="6" x2="16" y2="6"/><line x1="16" y1="10" x2="16" y2="10"/><line x1="12" y1="10" x2="12" y2="10"/><line x1="8" y1="10" x2="8" y2="10"/><line x1="16" y1="14" x2="16" y2="14"/><line x1="12" y1="14" x2="12" y2="14"/><line x1="8" y1="14" x2="8" y2="14"/><line x1="16" y1="18" x2="16" y2="18"/><line x1="12" y1="18" x2="12" y2="18"/><line x1="8" y1="18" x2="8" y2="18"/></svg>',
        'fa-coins': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="8" r="6"/><path d="M18.09 10.37A6 6 0 1 1 10.34 18"/><path d="M7 6h1v4"/><path d="m16.71 13.88.7.71-2.82 2.82"/></svg>',
        'fa-users': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/></svg>',
        'fa-rocket': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/><path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/><path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/><path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/></svg>',
        'fa-tasks': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 11H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m0-7v7m0-7a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2m0 0H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m6-10V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5"/></svg>',
        'fa-calendar': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>',
        'fa-user-friends': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/></svg>',
        'fa-box': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/></svg>',
        'fa-life-ring': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="4"/><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"/><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"/><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"/><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"/><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"/></svg>',
        'fa-whatsapp': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"/><path d="M9 10a3 3 0 0 1 6 0c0 2-3 3-3 3"/><path d="M9 17h.01"/></svg>',
        'fa-file': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/></svg>',
        'fa-bullhorn': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 11 18-5v12L3 14v-3z"/><path d="M11.6 16.8a3 3 0 1 1-5.8-1.6"/></svg>',
        'fa-shopping-cart': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="21" r="1"/><circle cx="19" cy="21" r="1"/><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/></svg>',
        'fa-robot': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="10" rx="2" ry="2"/><circle cx="12" cy="5" r="2"/><path d="M12 7v4"/><line x1="8" y1="16" x2="8" y2="16"/><line x1="16" y1="16" x2="16" y2="16"/></svg>',
        'fa-inbox': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22,12 18,12 15,21 9,21 6,12 2,12"/><path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"/></svg>',
        'fa-sliders': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" y1="21" x2="4" y2="14"/><line x1="4" y1="10" x2="4" y2="3"/><line x1="12" y1="21" x2="12" y2="12"/><line x1="12" y1="8" x2="12" y2="3"/><line x1="20" y1="21" x2="20" y2="16"/><line x1="20" y1="12" x2="20" y2="3"/><line x1="1" y1="14" x2="7" y2="14"/><line x1="9" y1="8" x2="15" y2="8"/><line x1="17" y1="16" x2="23" y2="16"/></svg>',
        'fa-tag': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/><line x1="7" y1="7" x2="7.01" y2="7"/></svg>',
        'fa-building': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12h4"/><path d="M6 16h4"/><path d="M16 12h2"/><path d="M16 16h2"/><path d="M16 20h2"/><path d="M6 20h4"/><path d="M2 22h20"/></svg>',
        'fa-user-plus': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="8.5" cy="7" r="4"/><line x1="20" y1="8" x2="20" y2="14"/><line x1="23" y1="11" x2="17" y2="11"/></svg>',
        'fa-dollar-sign': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"/><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg>',
        'fa-cogs': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 16.24l-4.24 4.24m12.02-12.02l-4.24 4.24M7.76 7.76L3.52 3.52"/></svg>',
        'default': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><rect x="9" y="9" width="6" height="6"/></svg>'
    };

    // Function to check and fix missing icons
    function fixMissingIcons() {
        const iconContainers = document.querySelectorAll('.dash-micon');

        iconContainers.forEach(container => {
            const icon = container.querySelector('i');
            if (icon && !container.querySelector('svg')) {
                // Check if FontAwesome is working
                const computedStyle = window.getComputedStyle(icon, '::before');
                const content = computedStyle.getPropertyValue('content');

                // If no content or content is "none", use SVG fallback
                if (!content || content === 'none' || content === '""' || content === 'normal') {
                    const iconClass = icon.className;
                    let svgKey = 'default';

                    // Determine which SVG to use based on FontAwesome class
                    if (iconClass.includes('fa-home')) svgKey = 'fa-home';
                    else if (iconClass.includes('fa-chart')) svgKey = 'fa-chart-pie';
                    else if (iconClass.includes('fa-calculator')) svgKey = 'fa-calculator';
                    else if (iconClass.includes('fa-coins')) svgKey = 'fa-coins';
                    else if (iconClass.includes('fa-users')) svgKey = 'fa-users';
                    else if (iconClass.includes('fa-rocket')) svgKey = 'fa-rocket';
                    else if (iconClass.includes('fa-tasks')) svgKey = 'fa-tasks';
                    else if (iconClass.includes('fa-calendar')) svgKey = 'fa-calendar';
                    else if (iconClass.includes('fa-user-friends')) svgKey = 'fa-user-friends';
                    else if (iconClass.includes('fa-box')) svgKey = 'fa-box';
                    else if (iconClass.includes('fa-life-ring')) svgKey = 'fa-life-ring';
                    else if (iconClass.includes('fa-whatsapp')) svgKey = 'fa-whatsapp';
                    else if (iconClass.includes('fa-file')) svgKey = 'fa-file';
                    else if (iconClass.includes('fa-bullhorn')) svgKey = 'fa-bullhorn';
                    else if (iconClass.includes('fa-shopping')) svgKey = 'fa-shopping-cart';
                    else if (iconClass.includes('fa-robot')) svgKey = 'fa-robot';
                    else if (iconClass.includes('fa-inbox')) svgKey = 'fa-inbox';
                    else if (iconClass.includes('fa-sliders')) svgKey = 'fa-sliders';
                    else if (iconClass.includes('fa-tag')) svgKey = 'fa-tag';
                    else if (iconClass.includes('fa-building')) svgKey = 'fa-building';
                    else if (iconClass.includes('fa-user-plus')) svgKey = 'fa-user-plus';
                    else if (iconClass.includes('fa-dollar-sign')) svgKey = 'fa-dollar-sign';
                    else if (iconClass.includes('fa-cogs')) svgKey = 'fa-cogs';

                    // Hide the original icon and add SVG
                    icon.style.display = 'none';
                    container.insertAdjacentHTML('beforeend', svgIcons[svgKey]);
                    container.classList.add('has-svg');

                    // Style the SVG - let flexbox handle centering
                    const svg = container.querySelector('svg');
                    if (svg) {
                        svg.style.width = '22px';
                        svg.style.height = '22px';
                        svg.style.stroke = '#ffffff';
                        svg.style.fill = 'none';
                        svg.style.strokeWidth = '2.5';
                        svg.style.strokeLinecap = 'round';
                        svg.style.strokeLinejoin = 'round';
                        svg.style.display = 'block';
                        svg.style.flexShrink = '0';
                    }
                }
            }
        });
    }

    // Run immediately
    fixMissingIcons();

    // Run again after FontAwesome should have loaded
    setTimeout(fixMissingIcons, 100);
    setTimeout(fixMissingIcons, 500);
    setTimeout(fixMissingIcons, 1000);

    // Also run when fonts are loaded
    if (document.fonts) {
        document.fonts.ready.then(fixMissingIcons);
    }

    // Function to convert hex color to RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Function to update custom color RGB values
    function updateCustomColorRgb() {
        const customColor = getComputedStyle(document.documentElement).getPropertyValue('--color-customColor');
        if (customColor && customColor.trim() !== '') {
            const rgb = hexToRgb(customColor.trim());
            if (rgb) {
                document.documentElement.style.setProperty('--color-customColor-rgb', `${rgb.r}, ${rgb.g}, ${rgb.b}`);
            }
        }
    }

    // Update RGB values when custom color changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                updateCustomColorRgb();
            }
        });
    });

    // Start observing
    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['style']
    });

    // Initial update
    updateCustomColorRgb();

    // Settings menu mobile optimization
    function optimizeSettingsMenu() {
        const settingsItems = document.querySelectorAll('.dash-submenu .dash-item');

        settingsItems.forEach(item => {
            const link = item.querySelector('.dash-link');
            if (link) {
                // Ensure proper touch targets on mobile
                link.style.minHeight = '44px';
                link.style.display = 'flex';
                link.style.alignItems = 'center';

                // Add touch feedback
                link.addEventListener('touchstart', function() {
                    this.style.backgroundColor = 'rgba(var(--theme-color-rgb), 0.1)';
                });

                link.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            }
        });
    }

    // Run optimization
    optimizeSettingsMenu();

    // Re-run when DOM changes (for dynamic content)
    const settingsObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                optimizeSettingsMenu();
            }
        });
    });

    // Start observing
    settingsObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
});
</script>

<?php if(isset($setting['cust_theme_bg']) && $setting['cust_theme_bg'] == 'on'): ?>
    <nav class="dash-sidebar light-sidebar transprent-bg">
    <?php else: ?>
        <nav class="dash-sidebar light-sidebar ">
<?php endif; ?>
<div class="navbar-wrapper">
    <div class="m-header main-logo">
        <a href="#" class="b-brand">

            <?php if($setting['cust_darklayout'] && $setting['cust_darklayout'] == 'on'): ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logos) && !empty($company_logos) ? $company_logos : 'logo-light.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php else: ?>
                <img src="<?php echo e($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') . '?' . time()); ?>"
                    alt="<?php echo e(config('app.name', 'ERPGo-SaaS')); ?>" class="logo logo-lg">
            <?php endif; ?>

        </a>

    </div>
    <div class="navbar-content">
        <?php if(\Auth::user()->type != 'client'): ?>
            <ul class="dash-navbar">
                <?php if(Request::segment(1) != 'settings'): ?>
                <!--------------------- Start Dashboard ----------------------------------->
                <?php if(Gate::check('show hrm dashboard') ||
                        Gate::check('show project dashboard') ||
                        Gate::check('show account dashboard') ||
                        Gate::check('show crm dashboard') ||
                        Gate::check('show pos dashboard')): ?>
                    <li
                        class="dash-item dash-hasmenu
                                <?php echo e(Request::segment(1) == null ||
                                Request::segment(1) == 'account-dashboard' ||
                                Request::segment(1) == 'hrm-dashboard' ||
                                Request::segment(1) == 'crm-dashboard' ||
                                Request::segment(1) == 'project-dashboard' ||
                                Request::segment(1) == 'account-statement-report' ||
                                Request::segment(1) == 'invoice-summary' ||
                                Request::segment(1) == 'sales' ||
                                Request::segment(1) == 'receivables' ||
                                Request::segment(1) == 'payables' ||
                                Request::segment(1) == 'bill-summary' ||
                                Request::segment(1) == 'product-stock-report' ||
                                Request::segment(1) == 'transaction' ||
                                Request::segment(1) == 'income-summary' ||
                                Request::segment(1) == 'expense-summary' ||
                                Request::segment(1) == 'income-vs-expense-summary' ||
                                Request::segment(1) == 'tax-summary' ||
                                Request::segment(1) == 'income report' ||
                                Request::segment(1) == 'report' ||
                                Request::segment(1) == 'reports-monthly-cashflow' ||
                                Request::segment(1) == 'reports-quarterly-cashflow' ||
                                Request::segment(1) == 'reports-payroll' ||
                                Request::segment(1) == 'report-leave' ||
                                Request::segment(1) == 'reports-monthly-attendance' ||
                                Request::segment(1) == 'reports-lead' ||
                                Request::segment(1) == 'reports-deal' ||
                                Request::segment(1) == 'pos-dashboard' ||
                                Request::segment(1) == 'reports-warehouse' ||
                                Request::segment(1) == 'reports-daily-purchase' ||
                                Request::segment(1) == 'reports-monthly-purchase' ||
                                Request::segment(1) == 'reports-daily-pos' ||
                                Request::segment(1) == 'reports-monthly-pos' ||
                                Request::segment(1) == 'reports-pos-vs-purchase'
                                    ? 'active dash-trigger'
                                    : ''); ?>">
                        <a href="<?php echo e(route('dashboard')); ?>" class="dash-link ">
                            <span class="dash-micon">
                                <i class="fas fa-home"></i>
                            </span>
                            <span class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                            <span class="dash-arrow"></span></a>
                        <ul class="dash-submenu">
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)) && Gate::check('show account dashboard')): ?>
                                <!-- <li
                                    class="dash-item <?php echo e(Request::segment(1) == null || Request::segment(1) == 'account-dashboard' ? ' active' : ''); ?>">
                                    <a class="dash-link"
                                        href="<?php echo e(route('dashboard')); ?>"><?php echo e(__(' Overview')); ?>

                                    </a>
                                </li> -->
                            <?php endif; ?>
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show crm dashboard')): ?>
                                    <!-- <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'crm-dashboard' ? ' active' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('CRM')); ?></a>
                                    </li> -->
                                <?php endif; ?>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>
                <!--------------------- End Dashboard ----------------------------------->

                <!--------------------- Start Reports | Analytics ----------------------------------->
                <?php if(
                    (!empty($userPlan) && (
                        ($user->type === 'company' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type === 'employee' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type !== 'company' && $user->type !== 'employee' && ($userPlan->account == 1 || $userPlan->crm == 1))
                    ))
                ): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'account-report' || Request::segment(1) == 'crm-report' ? 'active dash-trigger' : ''); ?>">
                        <a href="#" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-chart-pie"></i></span>
                            <span class="dash-mtext"><?php echo e(__('Reports | Analytics')); ?></span>
                            <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'account-report' || Request::segment(1) == 'report' || Request::segment(1) == 'reports-monthly-cashflow' || Request::segment(1) == 'reports-quarterly-cashflow' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('Accounts')); ?><span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('statement report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.account.statement' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.account.statement')); ?>"><?php echo e(__('Account Statement')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('invoice report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.invoice.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.invoice.summary')); ?>"><?php echo e(__('Invoice Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.sales' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.sales')); ?>"><?php echo e(__('Sales Report')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.receivables' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.receivables')); ?>"><?php echo e(__('Receivables')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(Request::route()->getName() == 'report.payables' ? ' active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.payables')); ?>"><?php echo e(__('Payables')); ?></a>
                                        </li>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bill report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.bill.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.bill.summary')); ?>"><?php echo e(__('Bill Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('stock report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.product.stock.report' ? ' active' : ''); ?>">
                                                <a href="<?php echo e(route('report.product.stock.report')); ?>" class="dash-link"><?php echo e(__('Product Stock')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('loss & profit report')): ?>
                                            <li class="dash-item <?php echo e(request()->is('reports-monthly-cashflow') || request()->is('reports-quarterly-cashflow') ? 'active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.monthly.cashflow')); ?>"><?php echo e(__('Cash Flow')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage transaction')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'transaction.index' || Request::route()->getName() == 'transfer.create' || Request::route()->getName() == 'transaction.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('transaction.index')); ?>"><?php echo e(__('Transaction')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.income.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.income.summary')); ?>"><?php echo e(__('Income Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expense report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.expense.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.expense.summary')); ?>"><?php echo e(__('Expense Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income vs expense report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.income.vs.expense.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.income.vs.expense.summary')); ?>"><?php echo e(__('Income VS Expense')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('tax report')): ?>
                                            <li class="dash-item <?php echo e(Request::route()->getName() == 'report.tax.summary' ? ' active' : ''); ?>">
                                                <a class="dash-link" href="<?php echo e(route('report.tax.summary')); ?>"><?php echo e(__('Tax Summary')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'crm-report' || Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? 'active dash-trigger' : ''); ?>">
                                    <a class="dash-link" href="#"><?php echo e(__('CRM')); ?><span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <li class="dash-item <?php echo e(request()->is('reports-lead') ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.lead')); ?>"><?php echo e(__('Lead')); ?></a>
                                        </li>
                                        <li class="dash-item <?php echo e(request()->is('reports-deal') ? 'active' : ''); ?>">
                                            <a class="dash-link" href="<?php echo e(route('report.deal')); ?>"><?php echo e(__('Deal')); ?></a>
                                        </li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <!-- Projects Menu Section -->
                            <li class="dash-item dash-hasmenu <?php echo e(in_array(Request::segment(1), ['team-performance', 'task-analysis', 'performance-analysis']) ? 'active dash-trigger' : ''); ?>">
                                <a class="dash-link" href="#">
                                   <?php echo e(__('Projects')); ?>

                                    <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                </a>
                                <ul class="dash-submenu">
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'team-performance' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.team-performance')); ?>"><?php echo e(__('Team Performance')); ?></a>
                                    </li>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'task-analysis' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.task-analysis')); ?>"><?php echo e(__('Task Analysis')); ?></a>
                                    </li>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'performance-analysis' ? 'active' : ''); ?>">
                                        <a class="dash-link" href="<?php echo e(route('projects.performance-analysis')); ?>"><?php echo e(__('Performance Analysis')); ?></a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                <?php endif; ?>
                <!--------------------- End Reports | Analytics ----------------------------------->

                <!--------------------- Start HRM ----------------------------------->

                
                
                                            

                <!--------------------- End HRM ----------------------------------->

                <!--------------------- Start Account ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                    <?php if(Gate::check('manage budget plan') || Gate::check('income vs expense report') ||
                            Gate::check('manage goal') || Gate::check('manage constant tax') ||
                            Gate::check('manage constant category') || Gate::check('manage constant unit') ||
                            Gate::check('manage constant custom field') || Gate::check('manage print settings') ||
                            Gate::check('manage customer') || Gate::check('manage vender') ||
                            Gate::check('manage proposal') || Gate::check('manage bank account') ||
                            Gate::check('manage bank transfer') || Gate::check('manage invoice') ||
                            Gate::check('manage revenue') || Gate::check('manage credit note') ||
                            Gate::check('manage bill') || Gate::check('manage payment') ||
                            Gate::check('manage debit note') || Gate::check('manage chart of account') ||
                            Gate::check('manage journal entry') || Gate::check('balance sheet report') ||
                            Gate::check('ledger report') || Gate::check('trial balance report') ): ?>
                        <li
                            class="dash-item dash-hasmenu
                                        <?php echo e(Request::route()->getName() == 'print-setting' ||
                                        Request::segment(1) == 'customer' || Request::segment(1) == 'vender' ||
                                        Request::segment(1) == 'proposal' || Request::segment(1) == 'bank-account' ||
                                        Request::segment(1) == 'bank-transfer' || Request::segment(1) == 'invoice' ||
                                        Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ||
                                        Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' ||
                                        Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' ||
                                        Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ||
                                        (Request::segment(1) == 'transaction' && Request::segment(2) != 'ledger' &&
                                            Request::segment(2) != 'balance-sheet-report' && Request::segment(2) != 'trial-balance') ||
                                        Request::segment(1) == 'goal' || Request::segment(1) == 'budget' ||
                                        Request::segment(1) == 'chart-of-account' || Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'ledger' || Request::segment(2) == 'balance-sheet' ||
                                        Request::segment(2) == 'trial-balance' || Request::segment(2) == 'profit-loss' ||
                                        Request::segment(1) == 'bill' || Request::segment(1) == 'expense' ||
                                        Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? ' active dash-trigger'
                                            : ''); ?>">
                            <a href="#!" class="dash-link"><span class="dash-micon"><i
                                        class="fas fa-calculator"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Accounting System ')); ?>

                                </span><span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu">

                                <?php if(Gate::check('manage bank account') || Gate::check('manage bank transfer')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bank-account' || Request::segment(1) == 'bank-transfer' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Banking')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item <?php echo e(Request::route()->getName() == 'bank-account.index' || Request::route()->getName() == 'bank-account.create' || Request::route()->getName() == 'bank-account.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('bank-account.index')); ?>"><?php echo e(__('Account')); ?></a>
                                            </li>
                                            <li
                                                class="dash-item <?php echo e(Request::route()->getName() == 'bank-transfer.index' || Request::route()->getName() == 'bank-transfer.create' || Request::route()->getName() == 'bank-transfer.edit' ? ' active' : ''); ?>">
                                                <a class="dash-link"
                                                    href="<?php echo e(route('bank-transfer.index')); ?>"><?php echo e(__('Transfer')); ?></a>
                                            </li>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage customer') ||
                                        Gate::check('manage proposal') ||
                                        Gate::check('manage invoice') ||
                                        Gate::check('manage revenue') ||
                                        Gate::check('manage credit note')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'customer' || Request::segment(1) == 'proposal' || Request::segment(1) == 'invoice' || Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Sales')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if(Gate::check('manage customer')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'customer' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('customer.index')); ?>"><?php echo e(__('Customer')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if(Gate::check('manage proposal')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'proposal' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('proposal.index')); ?>"><?php echo e(__('Estimate')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage invoice')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'invoice.index' || Request::route()->getName() == 'invoice.create' || Request::route()->getName() == 'invoice.edit' || Request::route()->getName() == 'invoice.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('invoice.index')); ?>"><?php echo e(__('Invoice')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage revenue')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'revenue.index' || Request::route()->getName() == 'revenue.create' || Request::route()->getName() == 'revenue.edit' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('revenue.index')); ?>"><?php echo e(__('Revenue')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage credit note')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'credit.note' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('credit.note')); ?>"><?php echo e(__('Credit Note')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage vender') ||
                                        Gate::check('manage bill') ||
                                        Gate::check('manage payment') ||
                                        Gate::check('manage debit note')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'bill' || Request::segment(1) == 'vender' || Request::segment(1) == 'expense' || Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Purchases')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if(Gate::check('manage vender')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::segment(1) == 'vender' ? 'active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('vender.index')); ?>"><?php echo e(__('Suppiler')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage bill')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'bill.index' || Request::route()->getName() == 'bill.create' || Request::route()->getName() == 'bill.edit' || Request::route()->getName() == 'bill.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                    href="<?php echo e(route('bill.index')); ?>"><?php echo e(__('Bill')); ?></a>
                                                </li>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'expense.index' || Request::route()->getName() == 'expense.create' || Request::route()->getName() == 'expense.edit' || Request::route()->getName() == 'expense.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('expense.index')); ?>"><?php echo e(__('Expense')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage payment')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'payment.index' || Request::route()->getName() == 'payment.create' || Request::route()->getName() == 'payment.edit' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('payment.index')); ?>"><?php echo e(__('Payment')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage debit note')): ?>
                                                <li
                                                    class="dash-item  <?php echo e(Request::route()->getName() == 'debit.note' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('debit.note')); ?>"><?php echo e(__('Debit Note')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage chart of account') ||
                                        Gate::check('manage journal entry') ||
                                        Gate::check('ledger report') ||
                                        Gate::check('bill report') ||
                                        Gate::check('income vs expense report') ||
                                        Gate::check('trial balance report')): ?>
                                    <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'chart-of-account' ||
                                        Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'profit-loss' ||
                                        Request::segment(2) == 'ledger' ||
                                        Request::segment(2) == 'trial-balance-report' ||
                                        Request::segment(2) == 'balance-sheet-report' ||
                                        Request::segment(2) == 'trial-balance' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? 'active dash-trigger'
                                            : ''); ?>">
                                        <a class="dash-link" href="#"><?php echo e(__('Double Entry')); ?><span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage chart of account')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'chart-of-account.index' || Request::route()->getName() == 'chart-of-account.show' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('chart-of-account.index')); ?>"><?php echo e(__('Chart of Accounts')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage journal entry')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'journal-entry.edit' ||
                                                    Request::route()->getName() == 'journal-entry.create' ||
                                                    Request::route()->getName() == 'journal-entry.index' ||
                                                    Request::route()->getName() == 'journal-entry.show'
                                                        ? ' active'
                                                        : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('journal-entry.index')); ?>"><?php echo e(__('Journal Account')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('ledger report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.ledger' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.ledger', 0)); ?>"><?php echo e(__('Ledger Summary')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bill report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.balance.sheet' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.balance.sheet')); ?>"><?php echo e(__('Balance Sheet')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income vs expense report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'report.profit.loss' ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('report.profit.loss')); ?>"><?php echo e(__('Profit & Loss')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('trial balance report')): ?>
                                                <li
                                                    class="dash-item <?php echo e(Request::route()->getName() == 'trial.balance' || (Request::route()->getName() == 'trial-balance-report') ? ' active' : ''); ?>">
                                                    <a class="dash-link"
                                                        href="<?php echo e(route('trial.balance')); ?>"><?php echo e(__('Trial Balance')); ?></a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </li>
                                <?php endif; ?>
                                <?php if(\Auth::user()->type == 'company'): ?>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'budget' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('budget.index')); ?>"><?php echo e(__('Budget Planner')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage goal')): ?>
                                    <li class="dash-item <?php echo e(Request::segment(1) == 'goal' ? 'active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('goal.index')); ?>"><?php echo e(__('Financial Goal')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <?php if(Gate::check('manage constant tax') ||
                                        Gate::check('manage constant category') ||
                                        Gate::check('manage constant unit') ||
                                        Gate::check('manage constant custom field')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' || Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' || Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ? 'active dash-trigger' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('taxes.index')); ?>"><?php echo e(__('Accounting Setup')); ?></a>
                                    </li>
                                <?php endif; ?>

                                <?php if(Gate::check('manage print settings')): ?>
                                    <li
                                        class="dash-item <?php echo e(Request::route()->getName() == 'print-setting' ? ' active' : ''); ?>">
                                        <a class="dash-link"
                                            href="<?php echo e(route('print.setting')); ?>"><?php echo e(__('Print Settings')); ?></a>
                                    </li>
                                <?php endif; ?>

                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Account ----------------------------------->

                <!--------------------- Start Finance ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1))): ?>
                    <?php if(Gate::check('manage customer') || Gate::check('manage vender') || Gate::check('manage proposal') ||
                         Gate::check('manage invoice') || Gate::check('manage revenue') || Gate::check('manage bill') ||
                         Gate::check('manage expense') || Gate::check('manage payment') || Gate::check('manage chart of account') ||
                         Gate::check('manage journal entry') || Gate::check('ledger report') || Gate::check('bill report') ||
                         Gate::check('manage bank account') || Gate::check('manage bank transfer')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'finance' ? 'active dash-trigger' : ''); ?>">
                            <a href="<?php echo e(route('finance.dashboard')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-chart-line"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Revenue Desk')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Finance ----------------------------------->

                <!--------------------- Start CRM ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1))): ?>
                    <?php if(Gate::check('manage lead') ||
                            Gate::check('manage deal') ||
                            Gate::check('manage form builder') ||
                            Gate::check('manage contract') ||
                            Gate::check('manage pipeline') ||
                            true): ?>
                        <li
                            class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'stages' || Request::segment(1) == 'labels' || Request::segment(1) == 'sources' || Request::segment(1) == 'lead_stages' || Request::segment(1) == 'pipelines' || Request::segment(1) == 'deals' || Request::segment(1) == 'leads' || Request::segment(1) == 'form_builder' || Request::segment(1) == 'contractType' || Request::segment(1) == 'form_response' || Request::segment(1) == 'contract' ? ' active dash-trigger' : ''); ?>">
                            <a href="<?php echo e(route('leads.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-users"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Lead Tracker')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End CRM ----------------------------------->

                <!--------------------- Start Booking Module ----------------------------------->

                <!-- <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1))): ?>
                    <?php if(Gate::check('manage booking') || Gate::check('view booking')): ?>
                        <li
                            class="dash-item <?php echo e(Request::segment(1) == 'bookings' || Request::segment(1) == 'appointment-bookings' || Request::segment(1) == 'appointments' || Request::segment(1) == 'calendar' && Request::segment(2) == 'events' ? ' active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('calendar.view')); ?>">
                                <span class="dash-micon"><i class="ti ti-calendar-event"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Calendar')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>  -->

                <!--------------------- End Booking Module ----------------------------------->

                <!--------------------- Start Project ----------------------------------->

                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('project')) || ($user->type === 'employee' && $userPlan->hasModule('project')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->project == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'project' || Request::segment(1) == 'projects' || request()->is('projects/*') ? 'active dash-trigger' : ''); ?>">
                            <a href="<?php echo e(route('projects.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-rocket"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Mission Control')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!--------------------- End Project ----------------------------------->

                <!--------------------- Start My Tasks (Personal Tasks) ----------------------------------->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('personal_tasks')) || ($user->type === 'employee' && $userPlan->hasModule('personal_tasks')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->personal_tasks == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage personal task')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'my-tasks' || Request::segment(1) == 'personal-tasks' || request()->is('personal-taskboard*') ? 'active dash-trigger' : ''); ?>">
                            <a href="<?php echo e(route('my-tasks.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-tasks"></i></span>
                                <span class="dash-mtext"><?php echo e(__('My Tasks')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End My Tasks (Personal Tasks) ----------------------------------->

                <!--------------------- Start Booking System ----------------------------------->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1))): ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage project task')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(request()->is('calendar*') ? 'active dash-trigger' : ''); ?>">
                            <a href="<?php echo e(route('task.calendar', ['all'])); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-calendar-alt"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Smart Scheduler')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End Booking System ----------------------------------->



                <!--------------------- Start User Managaement System ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && Gate::check('manage client')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'clients' ? ' active dash-trigger' : ''); ?>">

                        <a href="#!" class="dash-link "><span class="dash-micon"><i class="fas fa-user-friends"></i></span><span
                                class="dash-mtext"><?php echo e(__('User Management')); ?></span><span class="dash-arrow"><i
                                    data-feather="chevron-right"></i></span></a>
                        <ul class="dash-submenu">
                            <!-- <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                                <li class="dash-item <?php echo e(Request::route()->getName() == 'users.index' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('users.index')); ?>"><?php echo e(__('Users')); ?></a>
                                </li>
                            <?php endif; ?> -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage employee')): ?>
                                <li class="dash-item <?php echo e(Request::route()->getName() == 'employee.index' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('employee.index')); ?>" class="dash-link">
                                    
                                        <?php echo e(__('Employee')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage client')): ?>
                                <li
                                    class="dash-item <?php echo e(Request::route()->getName() == 'clients.index' || Request::segment(1) == 'clients' || Request::route()->getName() == 'clients.edit' ? ' active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('clients.index')); ?>"><?php echo e(__('Client')); ?></a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?>

                <!--------------------- End User Managaement System----------------------------------->


                <!--------------------- Start Products System ----------------------------------->

                <!-- <?php if(Gate::check('manage product & service')): ?>
                    <li class="dash-item dash-hasmenu">
                        <a href="#!" class="dash-link ">
                            <span class="dash-micon"><i class="fas fa-box"></i></span><span
                                class="dash-mtext"><?php echo e(__('Products System')); ?></span><span class="dash-arrow">
                                <i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            <?php if(Gate::check('manage product & service')): ?>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'productservice' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('productservice.index')); ?>"
                                        class="dash-link"><?php echo e(__('Product & Services')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(1) == 'productstock' ? 'active' : ''); ?>">
                                    <a href="<?php echo e(route('productstock.index')); ?>"
                                        class="dash-link"><?php echo e(__('Product Stock')); ?>

                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                <?php endif; ?> -->

                <!--------------------- End Products System ----------------------------------->


                <!--------------------- Start POs System ----------------------------------->
                
                
                <!--------------------- End POs System ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin'  && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active dash-trigger' : ''); ?>">
                        <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-life-ring"></i></span><span
                                class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                        </a>
                    </li>
                    
                    
                    <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                        <!-- OMX Flow Module - Show for users with omx flow module permission -->
                        

          

                        <!-- Templates - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'templates')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active dash-trigger' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                    <span class="dash-micon"><i class="fab fa-whatsapp"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Chat Flows')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Campaigns - Show only if user has specific permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if($user->hasModulePermission('omx_flow', 'campaigns')): ?>
                                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active dash-trigger' : ''); ?>">
                                    <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                        <span class="dash-micon"><i class="fas fa-bullhorn"></i></span><span
                                            class="dash-mtext"><?php echo e(__('Campaigns')); ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                      


                        <!-- Unified Inbox - Available for all users (no restrictions) -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                            <?php if(Gate::check('access omx flow')): ?>
                            <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : ''); ?>">
                    <a href="<?php echo e(route('company.modules.whatsapp-chat')); ?>" class="dash-link">
                    <span class="dash-micon"><i class="fas fa-inbox"></i></span><span
                            class="dash-mtext"><?php echo e(__('Unified Inbox')); ?></span> 
                    </a>
                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Automatish Module - Show for users with automatish module permission -->
                        <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('automatish')) || ($user->type === 'employee' && $userPlan->hasModule('automatish')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->automatish == 1))): ?>
                            <?php if(Gate::check('access automatish')): ?>
                                <?php
                                    $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                                ?>
                                <?php if($automatishModule && $automatishModule->sso_endpoint): ?>
                                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'automatish' ? 'active dash-trigger' : ''); ?>">
                                        <a href="<?php echo e(route('company.modules.automatish')); ?>" class="dash-link">
                                            <span class="dash-micon"><i class="fas fa-cogs"></i></span><span
                                                class="dash-mtext"><?php echo e(__('Auto Pilot')); ?></span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Support System - Show to system admin or staff with permission -->
                        <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('view support system'))): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-life-ring"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                            </a>
                        </li>
                        <?php endif; ?>

                    <?php endif; ?>
                <?php endif; ?>

                <?php if(\Auth::user()->type == 'company'): ?>
                    <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'notification_templates' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('notification-templates.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-notification"></i></span><span
                                class="dash-mtext"><?php echo e(__('Notification Template')); ?></span>
                        </a>
                    </li> -->
                <?php endif; ?>

                <!--------------------- Start System Setup ----------------------------------->

                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin'): ?>
                    <?php if(Gate::check('manage company plan') || Gate::check('manage order') || Gate::check('manage company settings')): ?>
                        <li
                            class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ||
                            Request::segment(1) == 'plans' ||
                            Request::segment(1) == 'stripe' ||
                            Request::segment(1) == 'order'
                                ? ' active dash-trigger'
                                : ''); ?>">
                            <a href="<?php echo e(route('settings.brand')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-sliders-h"></i></span><span
                                    class="dash-mtext"><?php echo e(__('System Settings')); ?></span>
                                <!-- <span class="dash-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span> -->
                            </a>
                            <ul class="dash-submenu">
                                <?php if(Gate::check('manage company settings')): ?>
                                    <!-- <li
                                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'settings' ? ' active' : ''); ?>">
                                        <a href="<?php echo e(route('settings')); ?>"
                                            class="dash-link"><?php echo e(__('System Settings')); ?>

                                        </a>
                                    </li> -->
                                <?php endif; ?>
                                <?php if(Gate::check('manage pricing plan')): ?>
                                    <li
                                        class="dash-item<?php echo e(Request::route()->getName() == 'pricing-plans.index' || Request::route()->getName() == 'stripe' ? ' active' : ''); ?>">
                                        <a href="<?php echo e(route('pricing-plans.index')); ?>"
                                            class="dash-link"><?php echo e(__('Setup Subscription Plan')); ?></a>
                                    </li>
                                <?php endif; ?>
                                <!-- <li
                                    class="dash-item<?php echo e(Request::route()->getName() == 'referral-program.company' ? ' active' : ''); ?>">
                                    <a href="<?php echo e(route('referral-program.company')); ?>"
                                        class="dash-link"><?php echo e(__('Referral Program')); ?></a>
                                </li> -->

                                <?php if(Gate::check('manage order') && Auth::user()->type == 'company'): ?>
                                    <!-- <li class="dash-item <?php echo e(Request::segment(1) == 'order' ? 'active' : ''); ?>">
                                        <a href="<?php echo e(route('order.index')); ?>" class="dash-link"><?php echo e(__('Order')); ?></a>
                                    </li> -->
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
                <?php endif; ?>




                <!--------------------- Start Settings ----------------------------------->
                <?php if(\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff'): ?>
                    <?php if(Request::segment(1) == 'settings'): ?>
                        <!-- Back to Main Menu -->
                        <li class="dash-item dash-hasmenu">
                            <a href="<?php echo e(route('dashboard')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-arrow-left"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Back to Main Menu')); ?></span>
                            </a>
                        </li>
                        
                        <!-- Settings Sections -->
                        <li class="dash-item dash-hasmenu <?php echo e(!isset($section) || $section == 'brand' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.brand')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-palette"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Brand Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'system' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.system')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-desktop"></i></span>
                                <span class="dash-mtext"><?php echo e(__('General Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'currency' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.currency')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-dollar-sign"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Currency Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'email' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.email')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-envelope"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Email Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'sso' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.sso')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-key"></i></span>
                                <span class="dash-mtext"><?php echo e(__('SSO Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'custom-fields' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.custom-fields')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-edit"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Custom Field Settings')); ?></span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e($section == 'tags' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('settings.tags')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-tags"></i></span>
                                <span class="dash-mtext"><?php echo e(__('Tag Settings')); ?></span>
                            </a>
                        </li>
                       
                    <?php else: ?>
                        <!-- Main System Settings Link -->
                 
                    <?php endif; ?>
                <?php endif; ?>
                <!--------------------- End Settings ----------------------------------->

                <!--------------------- End System Setup ----------------------------------->
            </ul>
        <?php endif; ?>
        <?php if(\Auth::user()->type == 'client'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage client dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home fa-2"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage deal')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'deals' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('deals.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Deals')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage contract')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'contract.index' || Request::route()->getName() == 'contract.show' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('contract.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext"><?php echo e(__('Contract')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'projects' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('projects.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-share"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
                <?php if(Gate::check('manage project')): ?>
                    <li
                        class="dash-item  <?php echo e(Request::route()->getName() == 'project_report.index' || Request::route()->getName() == 'project_report.show' ? 'active' : ''); ?>">
                        <a class="dash-link" href="<?php echo e(route('project_report.index')); ?>">
                            <span class="dash-micon"><i class="ti ti-chart-line"></i></span><span
                                class="dash-mtext"><?php echo e(__('Project Report')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage project task')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'taskboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('taskBoard.view', 'list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-list-check"></i></span><span
                                class="dash-mtext"><?php echo e(__('Tasks')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>



                
                

                <?php if(Gate::check('manage timesheet')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'timesheet-list' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('timesheet.list')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-clock"></i></span><span
                                class="dash-mtext"><?php echo e(__('Timesheet')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>



                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-headset fa-2"></i></span><span
                            class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                    </a>
                </li>

                <!-- WhatsApp Template - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'templates') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Campaign - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'campaigns') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Campaign')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Orders - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'whatsapp_orders') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-orders' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-orders')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Orders')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Flow - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1))): ?>
                    <?php if($user->hasModulePermission('omx_flow', 'whatsapp_flows') || Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            </ul>
        <?php endif; ?>
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff'): ?>
            <ul class="dash-navbar">
                <!-- Dashboard - Show only if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view system admin dashboard')): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'dashboard' ? ' active' : ''); ?>">
                    <a href="<?php echo e(route('system-admin.dashboard')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-home"></i></span><span
                            class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- White Label - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view white label')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' || Request::route()->getName() == 'user.userlog' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('users.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-tag"></i></span><span
                                class="dash-mtext"><?php echo e(__('White Label')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Sub-accounts - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view sub accounts')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'companies' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('system-admin.companies')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-building"></i></span><span
                                class="dash-mtext"><?php echo e(__('Sub-accounts')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- Staff Management - Only for System Admins -->
                <?php if(\Auth::user()->type == 'system admin'): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'system-admin' && Request::segment(2) == 'staff' ? ' active' : ''); ?>">
                    <a href="<?php echo e(route('system-admin.staff.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-user-plus"></i></span><span
                            class="dash-mtext"><?php echo e(__('Staff Management')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Role Management - Hidden from system admin, only show for other user types with permission -->
                <?php if(\Auth::user()->type != 'system admin' && \Auth::user()->can('view role management')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'roles.index' || Request::route()->getName() == 'roles.create' || Request::route()->getName() == 'roles.edit' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('roles.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shield"></i></span><span
                                class="dash-mtext"><?php echo e(__('Role Management')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                
                <!-- Plan Management - Show if user has permission or is system admin -->
                

                <!-- Pricing Plans - Show if user has permission or is system admin -->
                <?php if(\Auth::user()->type == 'system admin' || \Auth::user()->can('view pricing plans')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'pricing-plans' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('pricing-plans.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-dollar-sign"></i></span><span
                                class="dash-mtext"><?php echo e(__('Pricing Plans')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

 <!-- Support System - Show to system admin or staff with permission -->
 <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('view support system'))): ?>
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'support' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('support.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-life-ring"></i></span><span
                            class="dash-mtext"><?php echo e(__('Support System')); ?></span>
                    </a>
                </li>
                <?php endif; ?>

                <!-- Settings - Show only to system admin -->
                <?php if(\Auth::user()->type == 'system admin'): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'systems.index' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('systems.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-cogs"></i></span><span
                                class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <!-- WhatsApp Template - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                            class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-template')); ?>">
                                <?php echo e(__('Templates')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>">
                                <?php echo e(__('Campaign')); ?>

                            </a>
                        </li>
                    </ul>
                </li> -->

                <!-- WhatsApp Flow - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                            class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                    </a>
                </li> -->

                <!-- Bot Management - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                            class="dash-mtext"><?php echo e(__('Bot Management')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                <?php echo e(__('Bot Replies')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                <?php echo e(__('Bot Flows')); ?>

                            </a>
                        </li>
                    </ul>
                </li> -->
            </ul>
        <?php endif; ?>

        <?php if(\Auth::user()->type == 'super admin'): ?>
            <ul class="dash-navbar">
                <?php if(Gate::check('manage super admin dashboard')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'dashboard' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('client.dashboard.view')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home"></i></span><span
                                class="dash-mtext"><?php echo e(__('Dashboard')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage user')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('users.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-users"></i></span><span
                                class="dash-mtext"><?php echo e(__('Companies')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage pricing plan')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'pricing-plans' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('pricing-plans.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span><span
                                class="dash-mtext"><?php echo e(__('Pricing Plans')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li class="dash-item dash-hasmenu <?php echo e(request()->is('plan_request*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('plan_request.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-arrow-up-right-circle"></i></span><span
                            class="dash-mtext"><?php echo e(__('Plan Request')); ?></span>
                    </a>
                </li>

                <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == '' ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('referral-program.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-discount-2"></i></span><span
                            class="dash-mtext"><?php echo e(__('Referral Program')); ?></span>
                    </a>
                </li>

                <?php if(Gate::check('manage coupon')): ?>
                    <li class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'coupons' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('coupons.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-gift"></i></span><span
                                class="dash-mtext"><?php echo e(__('Coupon')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if(Gate::check('manage order')): ?>
                    <li class="dash-item dash-hasmenu  <?php echo e(Request::segment(1) == 'orders' ? 'active' : ''); ?>">
                        <a href="<?php echo e(route('order.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shopping-cart-plus"></i></span><span
                                class="dash-mtext"><?php echo e(__('Order')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <li
                    class="dash-item dash-hasmenu <?php echo e(Request::segment(1) == 'email_template' || Request::route()->getName() == 'manage.email.language' ? ' active dash-trigger' : 'collapsed'); ?>">
                    <a href="<?php echo e(route('email_template.index')); ?>" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-template"></i></span>
                        <span class="dash-mtext"><?php echo e(__('Email Template')); ?></span>
                    </a>
                </li>

                <?php echo $__env->make('landingpage::menu.landingpage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- OMX Flow for Super Admin - Show for users with omx flow module permission -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.omx-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                    class="dash-mtext"><?php echo e(__('OMX Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Template for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-template')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Template')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Campaign for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-campaign')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Campaign')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Orders for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-orders' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-orders')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-shopping-cart"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Orders')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- WhatsApp Flow for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : ''); ?>">
                            <a href="<?php echo e(route('company.modules.whatsapp-flow')); ?>" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                                    class="dash-mtext"><?php echo e(__('WhatsApp Flow')); ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- ChatBot Management for Super Admin - Show only if user has OMX Flow permissions -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access omx flow')): ?>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                            <a href="#!" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                    class="dash-mtext"><?php echo e(__('Chatbot')); ?></span>
                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                        <?php echo e(__('All Chatbots')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                        <?php echo e(__('Flow Maker')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <!-- Bot Management for Super Admin - Available for all users (no restrictions) -->
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                            class="dash-mtext"><?php echo e(__('Bot Management')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : ''); ?>">
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-replies')); ?>">
                                <?php echo e(__('Bot Replies')); ?>

                            </a>
                        </li>
                        <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : ''); ?>">
                            <a class="dash-link" href="<?php echo e(route('company.modules.bot-flows')); ?>">
                                <?php echo e(__('Bot Flows')); ?>

                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Unified Inbox for Super Admin - Available for all users (no restrictions) -->
                <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : ''); ?>">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-inbox"></i></span><span
                            class="dash-mtext"><?php echo e(__('Unified Inbox')); ?></span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'whatsapp-api-setup' || Request::segment(3) == 'facebook-api-setup' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('WhatsApp')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'whatsapp-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.whatsapp-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('Facebook')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'facebook-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'facebook-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.facebook-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dash-item dash-hasmenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'active dash-trigger' : ''); ?>">
                            <a class="dash-link" href="#!">
                                <?php echo e(__('Instagram')); ?>

                                <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu <?php echo e(Request::segment(2) == 'modules' && (Request::segment(3) == 'instagram-chat' || Request::segment(3) == 'instagram-api-setup') ? 'show' : ''); ?>">
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-chat' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-chat')); ?>">
                                        <?php echo e(__('Chat')); ?>

                                    </a>
                                </li>
                                <li class="dash-item <?php echo e(Request::segment(2) == 'modules' && Request::segment(3) == 'instagram-api-setup' ? 'active' : ''); ?>">
                                    <a class="dash-link" href="<?php echo e(route('company.modules.instagram-api-setup')); ?>">
                                        <?php echo e(__('API Setup')); ?>

                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <!-- Automatish Module - Show for super admin users with automatish module permission -->
                <?php if(!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('automatish')) || ($user->type === 'system admin'))): ?>
                    <?php if(Gate::check('access automatish')): ?>
                        <?php
                            $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                        ?>
                        <?php if($automatishModule && $automatishModule->sso_endpoint): ?>
                            <li class="dash-item dash-hasmenu">
                                <?php if(Auth::user()->type === 'company'): ?>
                                    <a href="<?php echo e(route('company.modules.automatish')); ?>" class="dash-link">
                                <?php else: ?>
                                    <a href="<?php echo e(route('module-integration.sso-login', $automatishModule->id)); ?>" class="dash-link">
                                <?php endif; ?>
                                    <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                        class="dash-mtext"><?php echo e(__('Automatish')); ?></span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if(Gate::check('manage system settings')): ?>
                    <li
                        class="dash-item dash-hasmenu <?php echo e(Request::route()->getName() == 'systems.index' ? ' active' : ''); ?>">
                        <a href="<?php echo e(route('systems.index')); ?>" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                                class="dash-mtext"><?php echo e(__('Settings')); ?></span>
                        </a>
                    </li>
                <?php endif; ?>

            </ul>
        <?php endif; ?>


    </div>
</div>
</nav>

<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/partials/admin/menu.blade.php ENDPATH**/ ?>