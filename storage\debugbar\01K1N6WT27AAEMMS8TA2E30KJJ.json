{"__meta": {"id": "01K1N6WT27AAEMMS8TA2E30KJJ", "datetime": "2025-08-02 10:57:57", "utime": **********.320471, "method": "GET", "uri": "/appointment-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": 1754132276.73615, "end": **********.320485, "duration": 0.5843350887298584, "duration_str": "584ms", "measures": [{"label": "Booting", "start": 1754132276.73615, "relative_start": 0, "end": **********.209128, "relative_end": **********.209128, "duration": 0.***************, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.209137, "relative_start": 0.*****************, "end": **********.320487, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.215784, "relative_start": 0.*****************, "end": **********.228761, "relative_end": **********.228761, "duration": 0.012976884841918945, "duration_str": "12.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.318075, "relative_start": 0.****************, "end": **********.318435, "relative_end": **********.318435, "duration": 0.0003600120544433594, "duration_str": "360μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET appointment-contacts", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getAppointmentContacts<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=234\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "appointment-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=234\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ContactController.php:234-282</a>"}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025990000000000003, "accumulated_duration_str": "25.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.248174, "duration": 0.02348, "duration_str": "23.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 90.342}, {"sql": "select * from `settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.29962, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 90.342, "width_percent": 4.771}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 74 and `is_active` = 1", "type": "query", "params": [], "bindings": [74, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.305668, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:243", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=243", "ajax": false, "filename": "ContactController.php", "line": "243"}, "connection": "new_omx_saas", "explain": null, "start_percent": 95.114, "width_percent": 2.539}, {"sql": "select `id`, `name`, `phone` from `deals` where `created_by` = 74 and `is_active` = 1", "type": "query", "params": [], "bindings": [74, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 259}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.310006, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:259", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=259", "ajax": false, "filename": "ContactController.php", "line": "259"}, "connection": "new_omx_saas", "explain": null, "start_percent": 97.653, "width_percent": 2.347}]}, "models": {"data": {"App\\Models\\Lead": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 3}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/appointment-contacts", "action_name": "appointment-contacts", "controller_action": "App\\Http\\Controllers\\ContactController@getAppointmentContacts", "uri": "GET appointment-contacts", "controller": "App\\Http\\Controllers\\ContactController@getAppointmentContacts<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=234\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=234\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ContactController.php:234-282</a>", "middleware": "web, verified, auth, XSS", "duration": "586ms", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-152630818 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-152630818\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261733998 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1261733998\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1634166134 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost:8000/calendar/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ik9USnB1b0V4NXBSODFqTXZRMkswQVE9PSIsInZhbHVlIjoiUlJ4RmFacFRZdC9jc28waTJkTEhTdjFJUkFNZEI5RjF1cjk2cG5GZUxhTjFEV0IrbU1BYmlUUEpwSmZ3cHVOdENMQjFYTTFyTytpRmU3RHVsOEJ5OWNYeCs0ZVJCWFQvbm4vTWxqdHA1SGxPbW5aR3lNVmJqc3k5bGdlTGtmUDU5eFZwUEJJWDZEL3Rja2NHOXdNRG4xb2s5TWVEWkRVdXU3Q3dxOFZzelRHcG5pcHUrZ2tXMWtDZ3RDa3h5bzVnNkgxRlcwcWM4Sk9OeWRodG43bXhrMjBkSkZoWms1eFhxNEFWbUwxb2JOOEw4ZnowYjQ0am5TUVhmcnM4REIrcDBxS2c5eHN5L0FpWExWSGF5RnJQSWxLOTFIOWpJclBKNm13TzBETXRUY0RtdVNRbFR3Q0JqVkNxSFRCQlI4T2dqOHRBMXBFRitIdkoyQnBxTVdZZXd5ZzZHQ2c0SkU1MnJWVzBGRlhjMlFzM3FrSlN1bkllYVg4KzJ0Z2g2dnlPclF1NDJmaWhELzhKVGV2RFRRTnBKazVxY2ZYNnU1alMybWZDT3BTZi8zZU1nSStySmtsS245ODk1YXBxMFF5NjMzdHdobVRKdmNTeXVQY3VmRU1EWnB1S2I5ZUFlcWRKTU42QXBXTTRESmFFdWUvOENMdmk0UVptcnVRVXhhMmkiLCJtYWMiOiIzM2EzZTg5ZmE5YmMxN2IxM2FlY2JjYmY2OTU0MDhlZTFjNWVlN2M0ZTliYTFmOWRlZGExMDc0M2Q5NzQxOWIzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRqRTBOMi9qNXMxS0s0WitFL05ueUE9PSIsInZhbHVlIjoiQzZYM0xpcWRwV3RQV0lDdGZGYkw4YVcvbEVRTll0T3VjTTEvc2ZwNDdZNVRWVWN0cDRLRHVKS3pBb1pYemFPNzZqRmFIcGVVVmMwR2UrTmY2UTViSVNpdGgyU1pFZ3c5dnFxMC8xMk54eVBMdHMvTlYxWnppcnV3SEIrRjhiWjFOUlp3dFM4Q1Q2RDVqSGFEaEhqaGlwd2I1ZzBUb3MxWEsvSW9DQXh5ZUZNNjN0VG0wRUdNemt0bnNCMEczUWZjK29qclZJOVcwMW5ZUUM5bGJ0NGc1UnpoQk4zZ29iSHN0TVVzQzdJV1ZqS2x0aXVQRFpwUzg4MFJ0K21sWDJOdFMwcDM0NldLRk5uSU1ZeXM2ZWdTcERCZ1M3REdkV0ZtWDJORnlzZ0V5OWtZYU10TGcxSTVuUTZtMXViZzRCOXVoVllxOVVRYWpMVUZHckdHU3JUbEd6RVUxWGlMckJ6NEx1UEhGdERkSG9adm5KMUZ2YTB4b1gxRG9EZnlnRC9zOWZaUEhrNmg5VTdSTzJiZW5UTmRzc0w4M0FMQmMrQVBYbS9zR0NSK2ZZZ1FTT0pjZm14aHZTOExnbDlwUWFsL2pLTHZkRk9nSVViQ0I5aWR0Ym91eXpFOVZwanlYbk1McWQxb1hLTHdKdWtGVnNhdnd5S0VGMWpta0RqVTVQelciLCJtYWMiOiI4NTU4NWM3NGIyZTc0Y2VjYzczNmQxNzE2Y2NkN2FjZTUxYzNlYzVjNGRiYTU3ZDJmYTY0YTI2YTA5MTA0ZWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634166134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-323339482 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IOyP74H6NVLBEM6iYLI1eIKyvqXjXHBBIRaE9bBQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323339482\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1045238741 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:57:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045238741\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-452005547 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452005547\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/appointment-contacts", "action_name": "appointment-contacts", "controller_action": "App\\Http\\Controllers\\ContactController@getAppointmentContacts"}, "badge": null}}