<?php

namespace App\Services;

use App\Models\CalendarEvent;
use App\Models\EventWeeklyAvailability;
use App\Models\AppointmentBooking;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SlotGeneratorService
{
    /**
     * Generate appointment booking slots for a calendar event
     */
    public function generateSlotsForEvent(CalendarEvent $event)
    {
        Log::info("Starting slot generation for event ID: {$event->id}");
        
        try {
            // Get the event's weekly availability
            $weeklyAvailability = EventWeeklyAvailability::where('calendar_event_id', $event->id)->get();
            
            if ($weeklyAvailability->isEmpty()) {
                Log::warning("No weekly availability found for event ID: {$event->id}");
                return false;
            }
            
            // Parse event dates
            $startDate = Carbon::parse($event->start_date);
            $endDate = Carbon::parse($event->end_date);
            
            // Limit the date range to prevent excessive slot generation
            $maxEndDate = Carbon::now()->addMonths(6); // Only generate slots for next 6 months
            if ($endDate->gt($maxEndDate)) {
                Log::info("Limiting slot generation to 6 months from now for event ID: {$event->id}");
                $endDate = $maxEndDate;
            }
            
            Log::info("Event date range: {$startDate->toDateString()} to {$endDate->toDateString()}");
            
            $slotsCreated = 0;
            $batchSize = 100; // Process in batches to avoid memory issues
            $currentBatch = [];
            
            // Loop through each date in the event range
            $currentDate = $startDate->copy();
            while ($currentDate->lte($endDate)) {
                $dayOfWeek = strtolower($currentDate->format('l')); // monday, tuesday, etc.
                
                // Find availability for this day of week
                $dayAvailability = $weeklyAvailability->where('day_of_week', $dayOfWeek)->first();
                
                if ($dayAvailability) {
                    $slotsForDay = $this->generateSlotsForDayBatch(
                        $event,
                        $currentDate,
                        $dayAvailability,
                        $currentBatch
                    );
                    $slotsCreated += $slotsForDay;
                    
                    // Insert batch if it reaches the batch size
                    if (count($currentBatch) >= $batchSize) {
                        $this->insertBatchSlots($currentBatch);
                        $currentBatch = [];
                    }
                }
                
                $currentDate->addDay();
            }
            
            // Insert remaining slots
            if (!empty($currentBatch)) {
                $this->insertBatchSlots($currentBatch);
            }
            
            Log::info("Generated {$slotsCreated} slots for event ID: {$event->id}");
            return $slotsCreated;
            
        } catch (\Exception $e) {
            Log::error("Error generating slots for event ID: {$event->id} - " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Generate slots for a specific day (batch version)
     */
    private function generateSlotsForDayBatch(CalendarEvent $event, Carbon $date, EventWeeklyAvailability $availability, &$batch)
    {
        $slotsCreated = 0;

        // Parse start and end times for the day
        $startTime = Carbon::parse($availability->start_time)->format('H:i:s');
        $endTime = Carbon::parse($availability->end_time)->format('H:i:s');

        $dayStart = Carbon::parse($date->toDateString() . ' ' . $startTime);
        $dayEnd = Carbon::parse($date->toDateString() . ' ' . $endTime);
        
        // Get event duration in minutes (ensure it's an integer)
        $duration = (int) ($event->duration ?? 60);
        
        Log::info("Generating slots for {$date->toDateString()} from {$dayStart->format('H:i')} to {$dayEnd->format('H:i')} with {$duration}min duration");
        
        // Generate time slots
        $currentSlot = $dayStart->copy();
        while ($currentSlot->copy()->addMinutes($duration)->lte($dayEnd)) {
            $slotEndTime = $currentSlot->copy()->addMinutes($duration);
            
            // Check if slot already exists
            $existingSlot = AppointmentBooking::where('event_id', $event->id)
                ->where('event_date', $date->toDateString())
                ->where('time_slots', $currentSlot->format('H:i'))
                ->first();
            
            if (!$existingSlot) {
                // Add to batch instead of creating immediately
                $batch[] = [
                    'event_id' => $event->id,
                    'event_location' => $event->location ?? 'online',
                    'event_location_value' => $this->getLocationValue($event),
                    'event_date' => $date->toDateString(),
                    'time_zone' => 'UTC', // You can make this configurable
                    'time_slots' => $currentSlot->format('H:i') . '-' . $slotEndTime->format('H:i'),
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                
                $slotsCreated++;
                Log::debug("Added slot to batch: {$date->toDateString()} {$currentSlot->format('H:i')}-{$slotEndTime->format('H:i')}");
            }
            
            $currentSlot->addMinutes($duration);
        }
        
        return $slotsCreated;
    }

    /**
     * Insert a batch of slots
     */
    private function insertBatchSlots($batch)
    {
        if (!empty($batch)) {
            AppointmentBooking::insert($batch);
            Log::info("Inserted batch of " . count($batch) . " slots");
        }
    }
    
    /**
     * Get location value based on event location type
     */
    private function getLocationValue(CalendarEvent $event)
    {
        switch ($event->location) {
            case 'in_person':
                return $event->physical_address ?? 'Physical Location';
            case 'zoom':
            case 'skype':
            case 'meet':
            case 'others':
                return $event->meet_link ?? 'Online Meeting';
            default:
                return 'To be determined';
        }
    }
    
    /**
     * Regenerate all slots for an event (useful for updates)
     */
    public function regenerateSlotsForEvent(CalendarEvent $event)
    {
        Log::info("Regenerating slots for event ID: {$event->id}");
        
        // Delete existing slots
        AppointmentBooking::where('event_id', $event->id)->delete();
        
        // Generate new slots
        return $this->generateSlotsForEvent($event);
    }
}
