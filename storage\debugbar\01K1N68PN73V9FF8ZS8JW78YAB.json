{"__meta": {"id": "01K1N68PN73V9FF8ZS8JW78YAB", "datetime": "2025-08-02 10:46:58", "utime": **********.472009, "method": "GET", "uri": "/calendar-events/staff-data", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[10:46:58] LOG.info: Staff Data Request - Company ID: 74, User ID: 74, User Type: company", "message_html": null, "is_string": false, "label": "info", "time": **********.448856, "xdebug_link": null, "collector": "log"}, {"message": "[10:46:58] LOG.info: Found staff data: 1 records", "message_html": null, "is_string": false, "label": "info", "time": **********.452253, "xdebug_link": null, "collector": "log"}, {"message": "[10:46:58] LOG.info: Staff data: [{\"id\":78,\"name\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\",\"type\":\"client\",\"profile\":\"http:\\/\\/localhost:8000\\/storage\\/avatar.png\"}]", "message_html": null, "is_string": false, "label": "info", "time": **********.465582, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 4, "start": 1754131617.878399, "end": **********.472047, "duration": 0.5936481952667236, "duration_str": "594ms", "measures": [{"label": "Booting", "start": 1754131617.878399, "relative_start": 0, "end": **********.381165, "relative_end": **********.381165, "duration": 0.****************, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.381179, "relative_start": 0.****************, "end": **********.472051, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "90.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.393727, "relative_start": 0.****************, "end": **********.409309, "relative_end": **********.409309, "duration": 0.015581846237182617, "duration_str": "15.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.469106, "relative_start": 0.****************, "end": **********.469486, "relative_end": **********.469486, "duration": 0.0003800392150878906, "duration_str": "380μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET calendar-events/staff-data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\CalendarEventController@getStaffData<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=1234\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "calendar-events.staff-data", "prefix": "/calendar-events", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=1234\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CalendarEventController.php:1234-1267</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0038699999999999997, "accumulated_duration_str": "3.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.43168, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 0, "width_percent": 60.207}, {"sql": "select * from `settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.445066, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "new_omx_saas", "explain": null, "start_percent": 60.207, "width_percent": 19.121}, {"sql": "select `id`, `name`, `email`, `type` from `users` where `created_by` = 74 and `type` in ('staff', 'Employee', 'employee', 'client') order by `type` asc, `name` asc", "type": "query", "params": [], "bindings": [74, "staff", "Employee", "employee", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 1249}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.449255, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "CalendarEventController.php:1249", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CalendarEventController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\CalendarEventController.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=1249", "ajax": false, "filename": "CalendarEventController.php", "line": "1249"}, "connection": "new_omx_saas", "explain": null, "start_percent": 79.328, "width_percent": 20.672}]}, "models": {"data": {"App\\Models\\User": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/logo/74-favicon.png?1754131598=\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/calendar-events/staff-data", "action_name": "calendar-events.staff-data", "controller_action": "App\\Http\\Controllers\\CalendarEventController@getStaffData", "uri": "GET calendar-events/staff-data", "controller": "App\\Http\\Controllers\\CalendarEventController@getStaffData<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=1234\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/calendar-events", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FCalendarEventController.php&line=1234\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CalendarEventController.php:1234-1267</a>", "middleware": "web, verified, auth, XSS", "duration": "595ms", "peak_memory": "56MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2011115843 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2011115843\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-374652063 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-374652063\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-898802628 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost:8000/calendar/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IklyTVZnSDNnOEJLeHdmRFVSN2cwb2c9PSIsInZhbHVlIjoiMmpVRVRmRmtsMWk4UXpibWhITHB4cTMrcExNTXFmeDZPdHhBRkJidkJFSkVjK05pdVZYMnpxdnRDdjBEQWV1dDliQ1ByWVFWTk5Fbkl3SFdMQ2EzcTF4QW5TMW9ZMDI2R1lsVDZpbXRkUDlKM1dqaEZ0WHFiOGpnMDRWOW1hRUJDZmk5ZUZteElQTUdwT1RLZ3VqclVDdGlCT2tOVzZnMDNpU2dWeEZMaWZFRXBrMFZSdFNpdTJzR283VHRWMlNlazNLUC9TcHd4TTN0Y0ZpR3FKUThNN245ZDlpS2xaSDMzYUhnK1piS3BVZk96QmFQd21tZmM5OGRlQXBMbHJnZjZobW9DTGdlVFNPdEdIVlBkVmRsdnQ5emhxYkwwM2VYRnJ2MkpiSCtZY1l0Q2hRN0IyM1RFdVg1TlI2d3h1UHRPKzVER0ltaytNMUlvVk9NOHNCWUlLSytyMFlaTFNMcWJEdHJtMUpSUUNkMU1CQlFOS0c4ZjBBcUtxU01QRFlyZStlSmNqdnF5bkhEajlZRFRjZ0ZnY2xMbDdLVWIyTENjMDRCamJzcmlldi83ejEwWksyc1F6ZkZsRTV4aHdQbUk2S3N3MzU3V3ZGc21icDZFZ09MODl5ZzZ4c3lkMjNUU1UrZlF6eWkrV1UzZm50VmEyUDVvaktKN2YwSktzdkEiLCJtYWMiOiIxYTk5ZTRkYWQ0MjVkYWZhNGM4NzVhNzYwNzRkM2Q3YjA3ZmM1ZDA4OWY4MTcyNmZmZDVmNTgyZmRjMzEwYzg0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ijl0aUJ0NGlPdHRQZkE2a2tMeWJVMUE9PSIsInZhbHVlIjoiWktDTzI0RkFpc3lBcXpCMXRqTmxvMzlITDFxVXZTeEQwOHNIcmNhMnMzSjdLMWVFdU9JYTlGNXVteFhUZzlibDVjMzROWXZhZGpuaFp4UWFlbzBBeGZpd0QwdldvOTU5UFRZTU9uVlU5S3NoNTE1alJlallPMUV0NUZFQmYwOFdSa0U2MVZEekx0enBVdXMrakNzeHZkOUhVVXdwWWR6VjROUllKUG1EbUwrcTNhUiswTnVUbituaE8vczhhZjUwVDBTeHZiWHVhMnRmR3ZrWmdPbmlIWGUxOEpoNTBjQVJUMjY3Q2lnUDBlelZBdGh4elJJaERwZStRTzhHY2xlWkFiZUJjZ1E1Q0llU0tsbFNnbGZOUEZyQklSN09JdWJiZWlJN29JR1ZCelQvdkVsK0JneW1idFRrcEtWL213T3hsMWUyYTdieUE1NDhWSkxYUEJFV2ttQXBsaWdvd0dWenhReXRTbTV0dHBQUk9xZTNjODlhQTJISlJPUTB1NFpVWWc5UldlVFlkSWNpdFlQYWNjZWRKcThxd1FhM2Q4MjhjRVNmTDZScThkN21mNElJdzN5UlF5NTZWK3VQbDJvNXFzbWpYOExSbkpzMlFKZzRxV2d6YlBRS3FiS2FkakVKTWtCZXJHUFB1WVhlemRSU0tHaWJnOWY5SFZFTlZKcysiLCJtYWMiOiJjN2I0MTBlMTFhMTQzODc1Y2U0OTU0NjM5NzkwYWRjZWFhN2QxZjgzODk2YTk3ZmFlYWJjODE3ZjhlNmEwYjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898802628\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1356173031 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IOyP74H6NVLBEM6iYLI1eIKyvqXjXHBBIRaE9bBQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356173031\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1134371206 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 02 Aug 2025 10:46:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134371206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-72822029 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bA8qfhnw4I5fOAYuVVR4g9QI5ZLCEl0BZawmhGja</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"69 characters\">http://localhost:8000/storage/uploads/logo/74-favicon.png?1754131598=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72822029\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/calendar-events/staff-data", "action_name": "calendar-events.staff-data", "controller_action": "App\\Http\\Controllers\\CalendarEventController@getStaffData"}, "badge": null}}