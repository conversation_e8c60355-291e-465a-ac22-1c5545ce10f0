{{ Form::open(['url' => route('tags.update', $tag->id), 'method' => 'PUT', 'class' => 'needs-validation', 'novalidate']) }}
<div class="modal-body">
    <div class="row">
        {{-- Tag Name --}}
        <div class="form-group col-md-12">
            {{ Form::label('name', __('Tag Name'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::text('name', $tag->name, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Tag Name'), 'id' => 'tag_name']) }}
        </div>

        {{-- Current Usage Info --}}
        <div class="form-group col-md-12">
            <label class="form-label">{{ __('Current Usage') }}</label>
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-3">
                        <span class="badge bg-info-subtle text-info">
                            <i class="ti ti-users me-1"></i>{{ $tag->leads()->count() }} {{ __('Leads') }}
                        </span>
                        <span class="badge bg-warning-subtle text-warning">
                            <i class="ti ti-briefcase me-1"></i>{{ $tag->projects()->count() }} {{ __('Projects') }}
                        </span>
                    </div>
                    <small class="text-muted">
                        <i class="ti ti-info-circle me-1"></i>{{ __('This tag is currently being used') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="text-end">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
        {{ Form::submit(__('Update Tag'), ['class' => 'btn btn-primary']) }}
    </div>
</div>
{{ Form::close() }} 