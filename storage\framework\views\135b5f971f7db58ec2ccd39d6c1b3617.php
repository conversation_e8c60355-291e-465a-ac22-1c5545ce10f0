

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Bookings')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Bookings')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
<style>
.custom-field-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
}

.custom-field-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

.custom-field-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 50%;
    color: white;
}

.field-value {
    word-break: break-word;
    line-height: 1.5;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Action buttons styling */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Table responsive improvements */
.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* Modal improvements */
.modal-header.bg-info,
.modal-header.bg-warning {
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('All Bookings')); ?></h5>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create booking')): ?>
                    <div>
                        <a href="<?php echo e(route('calendar.view')); ?>" class="btn btn-primary">
                            <i class="ti ti-plus me-1"></i><?php echo e(__('Create New Booking')); ?>

                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                            <i class="ti ti-refresh me-1"></i><?php echo e(__('Refresh')); ?>

                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <!-- Alert Messages -->
                <div id="alert-container" style="display: none;">
                    <div class="alert alert-dismissible fade show" role="alert" id="alert-message">
                        <span id="alert-text"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped" id="bookings-table">
                        <thead>
                            <tr>
                                <th><?php echo e(__('ID')); ?></th>
                                <th><?php echo e(__('Event Title')); ?></th>
                                <th class="regular-column"><?php echo e(__('Assigned Staff')); ?></th>
                                <th><?php echo e(__('Name')); ?></th>
                                <th><?php echo e(__('Email')); ?></th>
                                <th><?php echo e(__('Phone')); ?></th>
                                <th><?php echo e(__('Date')); ?></th>
                                <th><?php echo e(__('Time')); ?></th>
                                <th class="regular-column"><?php echo e(__('Payment')); ?></th>
                                <th class="regular-column"><?php echo e(__('Status')); ?></th>
                                <th class="payment-column" style="display: none;"><?php echo e(__('Duration')); ?></th>
                                <th class="payment-column" style="display: none;"><?php echo e(__('Payment Type')); ?></th>
                                <th class="payment-column" style="display: none;"><?php echo e(__('Amount')); ?></th>
                                <th class="payment-column" style="display: none;"><?php echo e(__('Payment Status')); ?></th>
                                <th class="payment-column" style="display: none;"><?php echo e(__('Transaction ID')); ?></th>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>
                                <th><?php echo e(__('Actions')); ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($booking->id); ?></td>
                                    <td><?php echo e($booking->event->title ?? 'N/A'); ?></td>
                                    <td class="regular-column">
                                        <?php if($booking->event && $booking->event->assignedStaff): ?>
                                            <span class="badge bg-primary">
                                                <i class="ti ti-user me-1"></i><?php echo e($booking->event->assignedStaff->name); ?>

                                            </span>
                                            <small class="d-block text-muted"><?php echo e(ucfirst($booking->event->assignedStaff->type)); ?></small>
                                        <?php else: ?>
                                            <span class="badge bg-light text-muted">
                                                <i class="ti ti-user-off me-1"></i><?php echo e(__('Not Assigned')); ?>

                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($booking->name); ?></td>
                                    <td><?php echo e($booking->email); ?></td>
                                    <td><?php echo e($booking->phone ?? 'N/A'); ?></td>
                                    <td><?php echo e($booking->date); ?></td>
                                    <td><?php echo e($booking->time); ?></td>
                                    <td class="regular-column">
                                        <?php if($booking->event && $booking->event->payment_required): ?>
                                            <?php if($booking->payment_amount): ?>
                                                <div class="d-flex flex-column">
                                                    <span class="badge <?php echo e($booking->payment_status === 'paid' ? 'bg-success' : ($booking->payment_status === 'pending' ? 'bg-warning' : 'bg-danger')); ?>">
                                                        <i class="ti ti-<?php echo e($booking->payment_status === 'paid' ? 'check' : ($booking->payment_status === 'pending' ? 'clock' : 'x')); ?> me-1"></i>
                                                        <?php echo e(ucfirst($booking->payment_status ?? 'pending')); ?>

                                                    </span>
                                                    <small class="text-muted"><?php echo e($booking->event->payment_currency ?? 'USD'); ?> <?php echo e(number_format($booking->payment_amount, 2)); ?></small>
                                                    <?php if($booking->payment_method): ?>
                                                        <small class="text-muted"><?php echo e(ucfirst($booking->payment_method)); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="badge bg-light text-muted"><?php echo e(__('No Payment')); ?></span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-light text-muted"><?php echo e(__('Free')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <!-- Payment Details Columns (Hidden by default) -->
                                    <td class="payment-column" style="display: none;">
                                        <?php echo e($booking->event->duration ?? 'N/A'); ?> min
                                    </td>
                                    <td class="payment-column" style="display: none;">
                                        <?php if($booking->payment_method): ?>
                                            <span class="badge <?php echo e($booking->payment_method === 'manual' ? 'bg-warning' : 'bg-success'); ?>">
                                                <?php echo e($booking->payment_method === 'manual' ? 'Offline' : 'Online'); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="payment-column" style="display: none;">
                                        <?php if($booking->payment_amount > 0): ?>
                                            <strong>₹<?php echo e(number_format($booking->payment_amount, 2)); ?></strong>
                                        <?php else: ?>
                                            <span class="text-muted">Free</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="payment-column" style="display: none;">
                                        <?php if($booking->payment_status === 'paid'): ?>
                                            <span class="badge bg-success"><i class="ti ti-check me-1"></i>Paid</span>
                                        <?php elseif($booking->payment_status === 'pending'): ?>
                                            <span class="badge bg-warning"><i class="ti ti-clock me-1"></i>Pending</span>
                                        <?php elseif($booking->payment_status === 'failed'): ?>
                                            <span class="badge bg-danger"><i class="ti ti-x me-1"></i>Failed</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><i class="ti ti-info-circle me-1"></i>Not Required</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="payment-column" style="display: none;">
                                        <small class="text-muted"><?php echo e($booking->payment_transaction_id ?? 'N/A'); ?></small>
                                    </td>
                                    <td class="regular-column" id="booking-<?php echo e($booking->id); ?>">
                                        <div class="dropdown status-dropdown">
                                            <?php
                                                $statusDisplay = $booking->statusDisplay;
                                            ?>
                                            <button class="btn btn-sm dropdown-toggle <?php echo e($statusDisplay['class']); ?>"
                                                    type="button"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false"
                                                    title="<?php echo e($statusDisplay['name']); ?>">
                                                <i class="<?php echo e($statusDisplay['icon']); ?> me-1"></i>
                                                <?php echo e($statusDisplay['name']); ?>

                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="changeBookingStatus(<?php echo e($booking->id); ?>, 'scheduled')">
                                                    <i class="ti ti-calendar-check me-2 text-primary"></i><?php echo e(__('Scheduled')); ?>

                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeBookingStatus(<?php echo e($booking->id); ?>, 'reschedule')">
                                                    <i class="ti ti-calendar-time me-2 text-warning"></i><?php echo e(__('Reschedule')); ?>

                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeBookingStatus(<?php echo e($booking->id); ?>, 'show_up')">
                                                    <i class="ti ti-check-circle me-2 text-success"></i><?php echo e(__('Show Up')); ?>

                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeBookingStatus(<?php echo e($booking->id); ?>, 'no_show')">
                                                    <i class="ti ti-x-circle me-2 text-danger"></i><?php echo e(__('No Show')); ?>

                                                </a></li>
                                                <li><a class="dropdown-item" href="#" onclick="changeBookingStatus(<?php echo e($booking->id); ?>, 'cancel')">
                                                    <i class="ti ti-ban me-2 text-secondary"></i><?php echo e(__('Cancel')); ?>

                                                </a></li>
                                            </ul>
                                        </div>
                                    </td>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>
                                    <td class="actions-column">
                                        <div class="btn-group" role="group">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('show booking')): ?>
                                            <button type="button" class="btn btn-sm btn-info"
                                                    onclick="viewBooking(<?php echo e($booking->id); ?>)"
                                                    title="<?php echo e(__('View Details')); ?>">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit booking')): ?>
                                            <button type="button" class="btn btn-sm btn-warning"
                                                    onclick="editBooking(<?php echo e($booking->id); ?>)"
                                                    title="<?php echo e(__('Edit Booking')); ?>">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete booking')): ?>
                                            <button type="button" class="btn btn-sm btn-danger"
                                                    onclick="deleteBooking(<?php echo e($booking->id); ?>)"
                                                    title="<?php echo e(__('Delete Booking')); ?>">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '16' : '15'); ?>" class="text-center"><?php echo e(__('No bookings found')); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Fields Modal -->
<div class="modal fade" id="customFieldsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-forms me-2"></i><?php echo e(__('Custom Fields Details')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" id="customFieldsContent">
                <!-- Custom fields will be displayed here -->
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Close')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Booking Modal -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>
<div class="modal fade" id="viewBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="ti ti-eye me-2"></i><?php echo e(__('Booking Details')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewBookingContent">
                <!-- Booking details will be displayed here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Close')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="ti ti-edit me-2"></i><?php echo e(__('Edit Booking')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBookingForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_booking_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_name" class="form-label"><?php echo e(__('Customer Name')); ?> *</label>
                            <input type="text" class="form-control" id="edit_booking_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_email" class="form-label"><?php echo e(__('Email')); ?> *</label>
                            <input type="email" class="form-control" id="edit_booking_email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_phone" class="form-label"><?php echo e(__('Phone')); ?></label>
                            <input type="text" class="form-control" id="edit_booking_phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_event" class="form-label"><?php echo e(__('Event')); ?></label>
                            <select class="form-control" id="edit_booking_event" disabled>
                                <option value=""><?php echo e(__('Loading events...')); ?></option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_date" class="form-label"><?php echo e(__('Date')); ?> *</label>
                            <input type="date" class="form-control" id="edit_booking_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_time" class="form-label"><?php echo e(__('Time')); ?></label>
                            <input type="time" class="form-control" id="edit_booking_time">
                        </div>
                    </div>

                    <div id="edit_custom_fields_container">
                        <!-- Custom fields will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Update Booking')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
function showCustomFields(customFields) {
    let content = '<div class="row">';

    // Ensure customFields is an object
    if (typeof customFields === 'string') {
        try {
            customFields = JSON.parse(customFields);
        } catch (e) {
            console.error('Error parsing custom fields:', e);
            customFields = {};
        }
    }

    if (customFields && typeof customFields === 'object' && Object.keys(customFields).length > 0) {
        Object.keys(customFields).forEach(function(key) {
            const value = customFields[key];
            const label = getFieldLabel(key);
            const formattedValue = formatFieldValue(key, value);
            const icon = getFieldIcon(key);

            content += `
                <div class="col-md-6 mb-3">
                    <div class="card custom-field-card h-100">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="custom-field-icon">
                                        <i class="ti ti-${icon}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-dark fw-bold">${label}</h6>
                                    <p class="mb-0 field-value text-muted">${formattedValue}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        content += `
            <div class="col-12">
                <div class="text-center py-4">
                    <i class="ti ti-info-circle fs-1 text-muted mb-3"></i>
                    <p class="text-muted">No custom fields available</p>
                </div>
            </div>
        `;
    }

    content += '</div>';

    $('#customFieldsContent').html(content);
    $('#customFieldsModal').modal('show');
}

// New function to show custom fields with proper labels and values
function showCustomFieldsModal(customFieldsData, contactName) {
    let content = '<div class="row">';

    // Add contact name header
    content += `
        <div class="col-12 mb-4">
            <div class="alert alert-primary">
                <h6 class="mb-0">
                    <i class="ti ti-user me-2"></i>Custom Fields for: <strong>${contactName}</strong>
                </h6>
            </div>
        </div>
    `;

    if (customFieldsData && Array.isArray(customFieldsData) && customFieldsData.length > 0) {
        customFieldsData.forEach(function(field) {
            const icon = getFieldIcon(field.type);
            const formattedValue = formatFieldValue(field.type, field.value);

            content += `
                <div class="col-md-6 mb-3">
                    <div class="card custom-field-card h-100 border-primary">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="custom-field-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="ti ti-${icon}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-primary fw-bold">${field.label}</h6>
                                    <div class="field-value-container">
                                        <span class="badge bg-light text-dark fs-6 p-2 w-100 text-start">${formattedValue}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        content += '<div class="col-12"><div class="alert alert-warning text-center"><i class="ti ti-info-circle me-2"></i>No custom fields available for this booking</div></div>';
    }

    content += '</div>';

    $('#customFieldsContent').html(content);
    $('#customFieldsModal').modal('show');
}

function getFieldLabel(fieldKey) {
    const fieldLabels = {
        'contact_type': 'Contact Type',
        'date_of_birth': 'Date of Birth',
        'business_type': 'Business Type',
        'business_gst_number': 'Business GST Number',
        'lead_value': 'Lead Value',
        'assigned_to_staff': 'Assigned to Staff',
        'contact_source': 'Contact Source',
        'opportunity_name': 'Opportunity Name',
        'postal_code': 'Postal Code',
        'full_name': 'Full Name',
        'specific_requirement': 'Any Specific Requirement',
        'used_whatsapp_api_chatbots': 'Have you used WhatsApp API and Chatbots ever',
        'generate_leads': 'How do you generate leads',
        'hear_about_omx_sales': 'Where did you hear about OMX Sales?',
        'city': 'City',
        'have_msme_certificate': 'Do you have MSME Certificate?',
        'whatsapp_number': 'WhatsApp Number',
        'meta_business_name': 'META Business Name',
        'have_website': 'Do you have a website?',
        'business_industry': 'Business Industry',
        'message': 'Message',
        'organization_task': 'Organization Task',
        'team_size': 'Team Size',
        'company_revenue': 'Company Revenue',
        'budget': 'What is your budget?',
        'real_estate_services': 'What type of real estate services do you offer?',
        'using_chatbot_tools': 'Are you currently using any chatbot or automation tools?',
        'implement_chatbot_timeframe': 'How soon are you looking to implement a chatbot?',
        'running_digital_ads': 'Are you currently running any digital ads?',
        'monthly_advertising_budget': 'Monthly Advertising Budget',
        'promoted_projects_count': 'How many real estate projects are you promoting?',
        'biggest_marketing_challenge': 'What is your biggest marketing challenge?',
        'property_price_range': 'What is the price range of the property you are selling?',
        'using_crm_software': 'Are you currently using any CRM software?',
        'advertising_on_third_party_platforms': 'Are you advertising on any third-party platforms?',
        'know_whatsapp_api': 'Do you know about the WhatsApp API?',
        'messages_volume': 'How many messages do you need to send?',
        'using_whatsapp_official_api': 'How are you currently doing WhatsApp Official API?',
        'monthly_lead_sales_volume': 'Monthly Lead/Sales Volume'
    };

    return fieldLabels[fieldKey] || fieldKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatFieldValue(fieldKey, value) {
    // Format specific field types
    if (fieldKey === 'date_of_birth' && value) {
        const date = new Date(value);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if ((fieldKey.includes('budget') || fieldKey.includes('revenue') || fieldKey.includes('value')) && value) {
        // Format currency values
        if (!isNaN(value)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }
    }

    if (fieldKey === 'team_size' && value) {
        return value + ' employees';
    }

    if ((fieldKey.includes('have_') || fieldKey.includes('using_') || fieldKey.includes('know_')) && value) {
        return value.charAt(0).toUpperCase() + value.slice(1);
    }

    return value || 'Not specified';
}

function getFieldIcon(fieldKey) {
    const fieldIcons = {
        'contact_type': 'phone',
        'date_of_birth': 'calendar',
        'business_type': 'building-store',
        'business_gst_number': 'file-text',
        'lead_value': 'currency-dollar',
        'assigned_to_staff': 'user',
        'contact_source': 'source',
        'opportunity_name': 'target',
        'postal_code': 'map-pin',
        'full_name': 'user-circle',
        'specific_requirement': 'list-details',
        'used_whatsapp_api_chatbots': 'brand-whatsapp',
        'generate_leads': 'trending-up',
        'hear_about_omx_sales': 'ear',
        'city': 'map-pin',
        'have_msme_certificate': 'certificate',
        'whatsapp_number': 'brand-whatsapp',
        'meta_business_name': 'brand-facebook',
        'have_website': 'world-www',
        'business_industry': 'building',
        'message': 'message',
        'organization_task': 'checklist',
        'team_size': 'users',
        'company_revenue': 'chart-line',
        'budget': 'wallet',
        'real_estate_services': 'home',
        'using_chatbot_tools': 'robot',
        'implement_chatbot_timeframe': 'clock',
        'running_digital_ads': 'ad',
        'monthly_advertising_budget': 'currency-dollar',
        'promoted_projects_count': 'hash',
        'biggest_marketing_challenge': 'alert-triangle',
        'property_price_range': 'home-dollar',
        'using_crm_software': 'database',
        'advertising_on_third_party_platforms': 'external-link',
        'know_whatsapp_api': 'brand-whatsapp',
        'messages_volume': 'message-circle',
        'using_whatsapp_official_api': 'api',
        'monthly_lead_sales_volume': 'chart-bar'
    };

    return fieldIcons[fieldKey] || 'info-circle';
}

// Alert system functions
function showAlert(message, type = 'success') {
    const alertContainer = $('#alert-container');
    const alertMessage = $('#alert-message');
    const alertText = $('#alert-text');

    // Set alert type and message
    alertMessage.removeClass('alert-success alert-danger alert-warning alert-info');
    alertMessage.addClass(`alert-${type}`);
    alertText.text(message);

    // Show alert
    alertContainer.show();

    // Auto-hide after 5 seconds
    setTimeout(function() {
        alertContainer.fadeOut();
    }, 5000);
}

// Admin CRUD Functions
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>

// Helper functions for status display
function getStatusColor(status) {
    const statusColors = {
        'scheduled': 'primary',
        'reschedule': 'warning',
        'show_up': 'success',
        'no_show': 'danger',
        'cancel': 'secondary'
    };
    return statusColors[status] || 'primary';
}

function getStatusText(status) {
    const statusTexts = {
        'scheduled': 'Scheduled',
        'reschedule': 'Reschedule',
        'show_up': 'Show Up',
        'no_show': 'No Show',
        'cancel': 'Cancel'
    };
    return statusTexts[status] || 'Scheduled';
}

// View booking details
function viewBooking(bookingId) {
    // Load booking data
    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                const customFieldDetails = response.custom_field_details || {};

                let content = `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="ti ti-user me-2"></i>Contact Information</h6>
                                    <p><strong>Name:</strong> ${booking.name}</p>
                                    <p><strong>Email:</strong> ${booking.email}</p>
                                    <p><strong>Phone:</strong> ${booking.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info"><i class="ti ti-calendar me-2"></i>Booking Details</h6>
                                    <p><strong>Event:</strong> ${booking.event ? booking.event.title : 'N/A'}</p>
                                    <p><strong>Date:</strong> ${booking.date}</p>
                                    <p><strong>Time:</strong> ${booking.time || 'N/A'}</p>
                                    <p><strong>Status:</strong> <span class="badge bg-${getStatusColor(booking.status)}">${getStatusText(booking.status)}</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add custom fields if available
                if (booking.custom_fields && booking.custom_fields_value) {
                    content += `
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h6 class="card-title text-success"><i class="ti ti-forms me-2"></i>Custom Fields</h6>
                                        <div class="row">
                    `;

                    // Process custom fields - custom_fields contains field IDs, custom_fields_value contains values
                    const fieldIds = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
                    const fieldValues = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

                    // Create a mapping of field IDs to values
                    const fieldData = {};
                    for(let i = 0; i < Math.min(fieldIds.length, fieldValues.length); i++) {
                        fieldData[fieldIds[i]] = fieldValues[i];
                    }

                    // Display each custom field with proper label and value
                    Object.keys(fieldData).forEach(function(fieldId) {
                        const fieldValue = fieldData[fieldId];
                        // Use custom field details if available, otherwise fallback to generic label
                        const fieldDetail = customFieldDetails[fieldId];
                        const fieldLabel = fieldDetail ? fieldDetail.name : `Custom Field ${fieldId}`;
                        const formattedValue = fieldValue || 'N/A';

                        content += `
                            <div class="col-md-6 mb-2">
                                <strong>${fieldLabel}:</strong> ${formattedValue}
                            </div>
                        `;
                    });

                    content += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                content += `
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title text-secondary"><i class="ti ti-info-circle me-2"></i>System Information</h6>
                                    <p><strong>Booking ID:</strong> ${booking.id}</p>
                                    <p><strong>Created:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
                                    <p><strong>Updated:</strong> ${new Date(booking.updated_at).toLocaleString()}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#viewBookingContent').html(content);
                $('#viewBookingModal').modal('show');
            } else {
                showAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Edit booking
function editBooking(bookingId) {
    // Load booking data
    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                const customFieldDetails = response.custom_field_details || {};

                // Populate form fields
                $('#edit_booking_id').val(booking.id);
                $('#edit_booking_name').val(booking.name);
                $('#edit_booking_email').val(booking.email);
                $('#edit_booking_phone').val(booking.phone || '');
                $('#edit_booking_date').val(booking.date);
                $('#edit_booking_time').val(booking.time || '');

                // Load events for dropdown
                loadEventsForEdit(booking.event_id);

                // Load custom fields with proper names
                loadCustomFieldsForEdit(booking, customFieldDetails);

                $('#editBookingModal').modal('show');
            } else {
                showAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Load events for edit dropdown
function loadEventsForEdit(selectedEventId) {
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                let options = '<option value=""><?php echo e(__("Select Event")); ?></option>';
                response.data.forEach(function(event) {
                    const selected = event.id == selectedEventId ? 'selected' : '';
                    options += `<option value="${event.id}" ${selected}>${event.title}</option>`;
                });
                $('#edit_booking_event').html(options);
            }
        },
        error: function(xhr) {
            console.error('Error loading events:', xhr.responseText);
        }
    });
}

// Load custom fields for edit
function loadCustomFieldsForEdit(booking, customFieldDetails) {
    let customFieldsHtml = '';

    if (booking.custom_fields && booking.custom_fields_value) {
        const fieldIds = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
        const fieldValues = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

        if (fieldIds.length > 0) {
            customFieldsHtml += '<h6 class="mb-3"><i class="ti ti-forms me-2"></i><?php echo e(__("Custom Fields")); ?></h6>';

            // Create a mapping of field IDs to values
            const fieldData = {};
            for(let i = 0; i < Math.min(fieldIds.length, fieldValues.length); i++) {
                fieldData[fieldIds[i]] = fieldValues[i];
            }

            // Display each custom field for editing
            Object.keys(fieldData).forEach(function(fieldId, index) {
                const fieldValue = fieldData[fieldId];
                const fieldLabel = customFieldDetails[fieldId] ? customFieldDetails[fieldId].name : `Custom Field ${fieldId}`;

                customFieldsHtml += `
                    <div class="mb-3">
                        <label for="edit_custom_field_${index}" class="form-label">${fieldLabel}</label>
                        <input type="text" class="form-control" id="edit_custom_field_${index}"
                               name="custom_fields_value[${index}]" value="${fieldValue || ''}"
                               data-field-id="${fieldId}">
                    </div>
                `;
            });
        }
    }

    $('#edit_custom_fields_container').html(customFieldsHtml);
}

// Delete booking
function deleteBooking(bookingId) {
    // Create a more user-friendly confirmation dialog
    const confirmDelete = confirm('<?php echo e(__("Are you sure you want to delete this booking?")); ?>\n\n<?php echo e(__("This action cannot be undone and will permanently remove all booking data.")); ?>');

    if (confirmDelete) {
        $.ajax({
            url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showAlert('<?php echo e(__("Booking deleted successfully")); ?>', 'success');
                    location.reload(); // Refresh the page to update the list
                } else {
                    showAlert('Error: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                showAlert('Error deleting booking', 'danger');
                console.error(xhr.responseText);
            }
        });
    }
}

// Handle edit booking form submission
$('#editBookingForm').on('submit', function(e) {
    e.preventDefault();

    const bookingId = $('#edit_booking_id').val();
    const formData = {
        name: $('#edit_booking_name').val(),
        email: $('#edit_booking_email').val(),
        phone: $('#edit_booking_phone').val(),
        date: $('#edit_booking_date').val(),
        time: $('#edit_booking_time').val(),
        custom_fields_value: {}
    };

    // Collect custom fields values with their field IDs
    $('#edit_custom_fields_container input[name^="custom_fields_value"]').each(function() {
        const fieldId = $(this).data('field-id');
        const fieldValue = $(this).val();
        if (fieldId) {
            formData.custom_fields_value[fieldId] = fieldValue;
        }
    });

    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                showAlert('<?php echo e(__("Booking updated successfully")); ?>', 'success');
                $('#editBookingModal').modal('hide');
                location.reload(); // Refresh the page to update the list
            } else {
                showAlert('Error: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showAlert('Error updating booking', 'danger');
            console.error(xhr.responseText);
        }
    });
});

// Status Management Functions
function changeBookingStatus(bookingId, newStatus) {
    console.log('Changing booking status:', bookingId, newStatus);

    if (!bookingId || !newStatus) {
        showAlert('<?php echo e(__("Invalid booking or status.")); ?>', 'error');
        return;
    }

    // Show loading state
    const $statusCell = $(`#booking-${bookingId} .status-dropdown`);
    const originalContent = $statusCell.html();
    $statusCell.html('<div class="spinner-border spinner-border-sm" role="status"></div>');

    // Make API call to update status
    $.ajax({
        url: `/bookings/${bookingId}/status`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            status: newStatus
        },
        success: function(response) {
            console.log('AJAX Success:', response);
            if (response.success) {
                showAlert('<?php echo e(__("Booking status updated successfully.")); ?>', 'success');
                // Refresh the page to show updated status
                location.reload();
            } else {
                showAlert('<?php echo e(__("Failed to update booking status.")); ?>', 'error');
                $statusCell.html(originalContent);
            }
        },
        error: function(xhr) {
            console.error('Error updating booking status:', xhr);
            let errorMessage = '<?php echo e(__("Failed to update booking status.")); ?>';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            showAlert(errorMessage, 'error');
            $statusCell.html(originalContent);
        }
    });
}

<?php endif; ?>
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/bookings/index.blade.php ENDPATH**/ ?>