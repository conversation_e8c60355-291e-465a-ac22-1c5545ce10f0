<div class="modal-body">
    <div class="row">
        <!-- Pipeline and Stage Selection -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ __('Import Settings') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="import_pipeline_id" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                <select class="form-control" id="import_pipeline_id" name="pipeline_id" required>
                                    <option value="">{{ __('Select Pipeline') }}</option>
                                    @php
                                        $pipelines = \App\Models\Pipeline::where('created_by', \Auth::user()->creatorId())->get();
                                    @endphp
                                    @foreach($pipelines as $pipeline)
                                        <option value="{{ $pipeline->id }}">{{ $pipeline->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="import_stage_id" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                <select class="form-control" id="import_stage_id" name="stage_id" required>
                                    <option value="">{{ __('Select Pipeline First') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV Data Mapping -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ __('Column Mapping') }}</h6>
                </div>
                <div class="card-body">
                    <div id="process_area" class="overflow-auto import-data-table">
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group col-12 d-flex justify-content-end col-form-label">
            <input type="button" value="{{ __('Cancel') }}" class="btn btn-secondary cancel" data-bs-dismiss="modal">
            <button type="submit" name="import" id="import" class="btn btn-primary ms-2" disabled>{{__('Import')}}</button>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        var total_selection = 0;
        var name = 0;
        var email = 0;
        var phone = 0;
        var column_data = {};
        var data = {};

        $('.cancel').on('click', function () {
            location.reload();
        });

        // Handle pipeline selection change
        $('#import_pipeline_id').on('change', function() {
            var pipelineId = $(this).val();
            var stageSelect = $('#import_stage_id');

            // Clear stages
            stageSelect.empty().append('<option value="">{{ __("Loading stages...") }}</option>');

            if (pipelineId) {
                // Load stages for selected pipeline
                $.ajax({
                    url: "{{ route('api.get-pipeline-stages') }}",
                    method: "POST",
                    data: {
                        pipeline_id: pipelineId,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

                        if (response.success && response.stages) {
                            $.each(response.stages, function(index, stage) {
                                stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                            });
                        }
                    },
                    error: function() {
                        stageSelect.empty().append('<option value="">{{ __("Error loading stages") }}</option>');
                        show_toastr('Error', 'Failed to load stages', 'error');
                    }
                });
            } else {
                stageSelect.empty().append('<option value="">{{ __("Select Pipeline First") }}</option>');
            }
        });

        $(document).on('change', '.set_column_data', function() {
            var column_data = {};
            var column_name = $(this).val();
            var column_number = $(this).data('column_number');

            $('.set_column_data').each(function() {
                var col_num = $(this).data('column_number');
                var selected = $(this).val();

                if (selected !== '') {
                    column_data[selected] = col_num;
                }
            });

            $('.set_column_data').each(function() {
                var $this = $(this);
                var col_num = $this.data('column_number');

                $this.find('option').each(function() {
                    var option_value = $(this).val();

                    if (option_value !== '' && option_value in column_data && column_data[option_value] !== col_num) {
                        $(this).prop('hidden', true);
                    } else {
                        $(this).prop('hidden', false);
                    }
                });
            });

            if (Object.keys(column_data).length > 0) {
                $("#import").removeAttr("disabled");
                data = {
                    name: column_data.name,
                    email: column_data.email,
                    phone: column_data.phone,
                    subject: column_data.subject,
                    notes: column_data.notes
                };
                console.log('Column data updated:', column_data);
                console.log('Data object updated:', data);
            } else {
                $('#import').attr('disabled', 'disabled');
                data = {};
            }
        });

        $('#import').on('click', function(event) {
            event.preventDefault();

            console.log('Import clicked, current data:', data);

            // Check if name column is selected
            var nameSelected = false;
            $('.set_column_data').each(function() {
                if ($(this).val() === 'name') {
                    nameSelected = true;
                    return false; // break the loop
                }
            });

            if (!nameSelected) {
                console.log('Name column not selected');
                show_toastr('Error', 'Please select at least the Name column', 'error');
                return false;
            }

            // Validate pipeline and stage selection
            var pipelineId = $('#import_pipeline_id').val();
            var stageId = $('#import_stage_id').val();

            if (!pipelineId) {
                show_toastr('Error', 'Please select a Pipeline', 'error');
                return false;
            }

            if (!stageId) {
                show_toastr('Error', 'Please select a Stage', 'error');
                return false;
            }

            // Add pipeline and stage to data
            data.pipeline_id = pipelineId;
            data.stage_id = stageId;
            data._token = "{{ csrf_token() }}";

            // Ensure all values are properly set
            console.log('Final data being sent:', data);

            $.ajax({
                url: "{{ route('contacts.import.data') }}",
                method: "POST",
                data: data,
                dataType: 'json',
                beforeSend: function() {
                    $('#import').attr('disabled', 'disabled');
                    $('#import').text('Importing...');
                },
                success: function(data) {
                    if (data.success == false) {
                        show_toastr('Error', data.message, 'error');
                    } else {
                        $('#import').attr('disabled', false);
                        $('#import').text('Import');
                        $('#upload_form')[0].reset();

                        if (data.html == true) {
                            $('#process_area').html(data.response);
                            $("button").hide();
                            show_toastr('Error', __('This data has not been inserted.'), 'error');

                        } else {
                            $('#message').html(data.response);
                            $('#commonModalOver').modal('hide')
                            show_toastr('Success', data.response, 'success');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Import AJAX Error:', xhr, status, error);
                    $('#import').attr('disabled', false);
                    $('#import').text('Import');

                    var errorMessage = 'Import failed. Please try again.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || response.error || errorMessage;
                        } catch (e) {
                            errorMessage = 'Server error: ' + xhr.status;
                        }
                    }

                    show_toastr('Error', errorMessage, 'error');
                }
            })
        });

        function SetData(data) {
            $('#process_area').html(data);
            $('[data-toggle="tooltip"]').tooltip();

            $(".set_column_data").each(function() {
                var column_name = $(this).val();
                var column_number = $(this).data('column_number');
                if (column_name != '') {
                    column_data[column_name] = column_number;
                }
            });

            total_selection = Object.keys(column_data).length;
            if (total_selection > 0) {
                $('#import').attr('disabled', false);
            }

            // Auto-select first pipeline if available
            var firstPipeline = $('#import_pipeline_id option:nth-child(2)');
            if (firstPipeline.length > 0) {
                $('#import_pipeline_id').val(firstPipeline.val()).trigger('change');
            }
        }

        // Make SetData globally available
        window.SetData = SetData;

        // Pipeline change handler
        $('#import_pipeline_id').on('change', function() {
            var pipelineId = $(this).val();
            var stageSelect = $('#import_stage_id');

            stageSelect.empty().append('<option value="">{{ __("Loading stages...") }}</option>');

            if (pipelineId) {
                $.ajax({
                    url: "{{ route('api.get-pipeline-stages') }}",
                    method: "POST",
                    data: {
                        pipeline_id: pipelineId,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');
                        if (response.stages && response.stages.length > 0) {
                            $.each(response.stages, function(index, stage) {
                                stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                            });
                        }
                    },
                    error: function() {
                        stageSelect.empty().append('<option value="">{{ __("Error loading stages") }}</option>');
                    }
                });
            } else {
                stageSelect.empty().append('<option value="">{{ __("Select Pipeline First") }}</option>');
            }
        });

        $('#commonModalOver').on('hidden.bs.modal', function () {
            location.reload();
        });
    });
</script>
