{{ Form::model($task, ['route' => ['projects.tasks.update',[$project->id, $task->id]], 'id' => 'edit_task', 'method' => 'POST', 'class'=>'needs-validation', 'novalidate']) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['project task']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('name', __('Task name'),['class' => 'form-label']) }}<x-required></x-required>
                {{ Form::text('name', null, ['class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Task Name')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('milestone_id', __('Milestone'),['class' => 'form-label']) }}
                <select class="form-control select" name="milestone_id" id="milestone_id">
                    <option value="0" class="text-muted">{{__('Select Milestone')}}</option>
                    @foreach($project->milestones as $m_val)
                        <option value="{{ $m_val->id }}" {{ ($task->milestone_id == $m_val->id) ? 'selected':'' }}>{{ $m_val->title }}</option>
                    @endforeach
                </select>
                <div class="text-xs mt-1">
                    {{ __('Create milestone here.') }} <a href="{{ route('projects.show', $project->id) }}"><b>{{ __('Create milestone') }}</b></a>
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                {{ Form::label('description', __('Description'),['class' => 'form-label']) }}
                <small class="form-text text-muted mb-2 mt-0">{{__('This textarea will autosize while you type')}}</small>
                {{ Form::textarea('description', null, ['class' => 'form-control','rows'=>'1','data-toggle' => 'autosize', 'placeholder'=>__('Enter Description')]) }}
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                {{ Form::label('tags', __('Tags'), ['class' => 'form-label']) }}
                {{ Form::select('tags[]', $tags ?? [], $task->tags->pluck('id')->toArray(), ['class' => 'form-control select2', 'multiple' => 'multiple', 'id' => 'tags-select', 'data-placeholder' => __('Select or create tags...')]) }}
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ __('You can select existing tags or type to create new ones. New tags will be created automatically.') }}
                </small>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('estimated_hrs', __('Estimated Hours'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('allocated total ').$hrs['allocated'].__(' hrs in other tasks')}}</small>
                {{ Form::number('estimated_hrs', null, ['class' => 'form-control','required' => 'required','min'=>'0','maxlength' => '8', 'placeholder'=>__('Enter Estimated Hours')]) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('priority', __('Priority'),['class' => 'form-label']) }}<x-required></x-required>
                <small class="form-text text-muted mb-2 mt-0">{{__('Set Priority of your task')}}</small>
                <select class="form-control select" name="priority" id="priority" required>
                    @foreach(\App\Models\ProjectTask::$priority as $key => $val)
                        <option value="{{ $key }}" {{ ($key == $task->priority) ? 'selected' : '' }} >{{ __($val) }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('start_date', __('Start Date'),['class' => 'form-label']) }}
                {{ Form::date('start_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
        <div class="col-6">
            <div class="form-group">
                {{ Form::label('end_date', __('End Date'),['class' => 'form-label']) }}
                {{ Form::date('end_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="form-label">{{__('Task members')}}</label>
        <small class="form-text text-muted mb-2 mt-0">{{__('Select users to assign this task to.')}}</small>
    </div>

    @php
        $assignedUsers = explode(',', $task->assign_to);
    @endphp

    @if($project->users->count() > 0)
        <div class="form-group">
            <select class="form-control select2" name="assign_to[]" id="edit_assign_to_members" multiple="multiple" data-placeholder="{{ __('Choose team members...') }}">
                @foreach($project->users as $user)
                    <option value="{{ $user->id }}" {{ in_array($user->id, $assignedUsers) ? 'selected' : '' }} data-avatar="{{ strtoupper(substr($user->name, 0, 1)) }}">
                        {{ $user->name }} ({{ $user->email }})
                    </option>
                @endforeach
            </select>
        </div>
    @else
        <div class="alert alert-warning">
            <i class="ti ti-alert-triangle me-2"></i>
            {{ __('No users assigned to this project. Please assign users to the project first.') }}
        </div>
    @endif
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Update')}}" class="btn  btn-primary">
</div>
{{Form::close()}}

<script>
$(document).ready(function() {
    // Initialize Select2 for task members
    $('#edit_assign_to_members').select2({
        theme: 'bootstrap4',
        width: '100%',
        placeholder: '{{ __("Choose team members...") }}',
        allowClear: true,
        closeOnSelect: false,
        templateResult: formatUser,
        templateSelection: formatUserSelection
    });

    // Custom formatting for user options in dropdown
    function formatUser(user) {
        if (!user.id) {
            return user.text;
        }
        
        const avatar = $(user.element).data('avatar');
        const userInfo = user.text.split('(');
        const userName = userInfo[0].trim();
        const userEmail = userInfo[1] ? userInfo[1].replace(')', '').trim() : '';
        
        return $(`
            <div class="d-flex align-items-center">
                <div class="user-avatar-small me-2">${avatar}</div>
                <div>
                    <div class="user-name-small">${userName}</div>
                    <small class="text-muted">${userEmail}</small>
                </div>
            </div>
        `);
    }

    // Custom formatting for selected users
    function formatUserSelection(user) {
        if (!user.id) {
            return user.text;
        }
        
        const userName = user.text.split('(')[0].trim();
        return userName;
    }

    // Form validation enhancement
    $('form').on('submit', function(e) {
        // Check if at least one user is selected
        const selectedUsers = $('#edit_assign_to_members').find('option:selected').length;
        if (selectedUsers === 0) {
            e.preventDefault();
            show_toastr('error', '{{ __("Please select at least one user to assign this task.") }}');
            return false;
        }
    });

    // Initialize Select2 for tags with tag creation functionality
    $('#tags-select').select2({
        placeholder: '{{ __("Select or create tags...") }}',
        allowClear: true,
        tags: true, // Enable tag creation
        width: '100%',
        dropdownParent: $('body'), // Ensure dropdown appears above modal
        minimumResultsForSearch: 0,
        closeOnSelect: false,
        
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#tags-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        },
        
        templateSelection: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        var data = e.params.data;
        
        // If this is a new tag, create it via AJAX
        if (data.newTag) {
            $.ajax({
                url: '{{ route("tags.store") }}',
                method: 'POST',
                data: {
                    name: data.text,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Update the option with the real tag ID
                        var option = $('#tags-select option[value="' + data.id + '"]');
                        option.val(response.tag.id);
                        option.text(data.text);
                        
                        // Update the selected option
                        $('#tags-select').val(function(idx, val) {
                            var newVal = val.filter(function(v) { return v !== data.id; });
                            newVal.push(response.tag.id);
                            return newVal;
                        });
                        
                        show_toastr('Success', '{{ __("Tag created successfully!") }}', 'success');
                    } else {
                        show_toastr('Error', response.message || '{{ __("Failed to create tag") }}', 'error');
                    }
                },
                error: function() {
                    show_toastr('Error', '{{ __("Failed to create tag") }}', 'error');
                }
            });
        }
    });
});
</script>

<style>
/* Custom styles for Select2 user dropdown */
.user-avatar-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.7rem;
}

.user-name-small {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.2;
}

/* Custom Select2 styling */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: 45px;
    border-radius: 0.5rem;
    border: 2px solid #e5e7eb;
    padding: 0.25rem;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus,
.select2-container--bootstrap4.select2-container--focus .select2-selection--multiple {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select2-container--bootstrap4 .select2-results__option {
    padding: 0.5rem;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #eff6ff;
    color: #3b82f6;
}
</style>

