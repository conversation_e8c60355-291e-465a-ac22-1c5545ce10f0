<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if tags table exists (it should from the rename migration)
        if (Schema::hasTable('tags')) {
            // Add missing columns to existing tags table
            Schema::table('tags', function (Blueprint $table) {
                if (!Schema::hasColumn('tags', 'is_active')) {
                    $table->boolean('is_active')->default(1)->after('created_by');
                }
            });

            // Add unique constraint if it doesn't exist
            try {
                Schema::table('tags', function (Blueprint $table) {
                    $table->unique(['name', 'created_by']);
                });
            } catch (\Exception $e) {
                // Constraint might already exist, ignore the error
            }
        } else {
            // If table doesn't exist, create it (fallback)
            Schema::create('tags', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->integer('created_by');
                $table->boolean('is_active')->default(1);
                $table->timestamps();

                // Add unique constraint for name per creator
                $table->unique(['name', 'created_by']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('tags')) {
            Schema::table('tags', function (Blueprint $table) {
                if (Schema::hasColumn('tags', 'is_active')) {
                    $table->dropColumn('is_active');
                }

                // Try to drop the unique constraint
                try {
                    $table->dropUnique(['name', 'created_by']);
                } catch (\Exception $e) {
                    // Constraint might not exist, ignore the error
                }
            });
        }
    }
};
