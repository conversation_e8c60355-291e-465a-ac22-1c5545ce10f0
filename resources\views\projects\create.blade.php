{{ Form::open(['url' => 'projects', 'method' => 'post', 'class'=>'needs-validation', 'novalidate']) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan && isset($plan->chatgpt) && $plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['project']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-sm-12 col-md-12">
            <div class="form-group">
                {{ Form::label('project_name', __('Project Name'), ['class' => 'form-label']) }}<x-required></x-required>
                {{ Form::text('project_name', null, ['class' => 'form-control','required'=>'required', 'placeholder'=>__('Enter Project Name')]) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6 col-md-6">
            <div class="form-group">
                {{ Form::label('start_date', __('Start Date'), ['class' => 'form-label']) }}
                {{ Form::date('start_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
        <div class="col-sm-6 col-md-6">
            <div class="form-group">
                {{ Form::label('end_date', __('End Date'), ['class' => 'form-label']) }}
                {{ Form::date('end_date', null, ['class' => 'form-control']) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6 col-md-6">
            <div class="form-group">
                {{ Form::label('client', __('Client'),['class'=>'form-label']) }}
                {!! Form::select('client', $clients, null,array('class' => 'form-control')) !!}
                <div class="text-xs mt-1">
                    {{ __('Create client here.') }} <a href="{{ route('clients.index') }}"><b>{{ __('Create client') }}</b></a>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-md-6">
            <div class="form-group">
                {{ Form::label('user', __('User'),['class'=>'form-label']) }}<x-required></x-required>
                {!! Form::select('user[]', $users, null,array('class' => 'form-control','required'=>'required')) !!}
                <div class="text-xs mt-1">
                    {{ __('Create user here.') }} <a href="{{ route('users.index') }}"><b>{{ __('Create user') }}</b></a>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-md-6">
            <div class="form-group">
                {{ Form::label('budget', __('Budget'), ['class' => 'form-label']) }}
                {{ Form::number('budget', null, ['class' => 'form-control', 'placeholder'=>__('Enter Project Budget')]) }}
            </div>
        </div>
        <div class="col-6 col-md-6">
            <div class="form-group">
                {{ Form::label('estimated_hrs', __('Estimated Hours'),['class' => 'form-label']) }}
                {{ Form::number('estimated_hrs', null, ['class' => 'form-control','min'=>'0','maxlength' => '8', 'placeholder'=>__('Enter Project Estimated Hours')]) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12">
            <div class="form-group">
                {{ Form::label('description', __('Description'), ['class' => 'form-label']) }}
                {{ Form::textarea('description', null, ['class' => 'form-control', 'rows' => '4', 'cols' => '50', 'placeholder'=>__('Enter Description')]) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12">
            <div class="form-group">
                {{ Form::label('tags', __('Tags'), ['class' => 'form-label']) }}
                {{ Form::select('tags[]', $tags ?? [], null, ['class' => 'form-control select2', 'multiple' => 'multiple', 'id' => 'tags-select', 'data-placeholder' => __('Select or create tags...')]) }}
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    {{ __('You can select existing tags or type to create new ones. New tags will be created automatically.') }}
                </small>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12">
            <div class="form-group">
                {{ Form::label('status', __('Status'), ['class' => 'form-label']) }}
                <select name="status" id="status" class="form-control main-element">
                    @foreach(\App\Models\Project::$project_status as $k => $v)
                        <option value="{{$k}}">{{__($v)}}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Create')}}" class="btn  btn-primary">
</div>
{{Form::close()}}

<style>
.select2-container--default .select2-results__option[aria-selected] {
    background-color: #e3f2fd;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2196f3;
    color: white;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    margin: 2px;
    font-size: 12px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
    font-weight: bold;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #ffebee;
}

.select2-container--default .select2-selection--multiple {
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    min-height: 45px;
    padding: 8px 12px;
}

.select2-container--default .select2-selection--multiple:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding: 0;
    line-height: 1.5;
}

.select2-container--default .select2-selection--multiple .select2-selection__input {
    margin: 0;
    padding: 0;
    line-height: 1.5;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    font-weight: bold;
    font-size: 16px;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear:hover {
    color: #333;
}
</style>

<script>
$(document).ready(function() {
    // Initialize Select2 for tags with tag creation functionality
    $('#tags-select').select2({
        placeholder: '{{ __("Select or create tags...") }}',
        allowClear: true,
        tags: true, // Enable tag creation
        width: '100%',
        dropdownParent: $('body'), // Ensure dropdown appears above modal
        minimumResultsForSearch: 0,
        closeOnSelect: false,
        
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#tags-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        },
        
        templateSelection: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        var data = e.params.data;
        
        // If this is a new tag, create it via AJAX
        if (data.newTag) {
            $.ajax({
                url: '{{ route("tags.store") }}',
                method: 'POST',
                data: {
                    name: data.text,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Update the option with the real tag ID
                        var option = $('#tags-select option[value="' + data.id + '"]');
                        option.val(response.tag.id);
                        option.text(data.text);
                        
                        // Update the selected option
                        $('#tags-select').val(function(idx, val) {
                            var newVal = val.filter(function(v) { return v !== data.id; });
                            newVal.push(response.tag.id);
                            return newVal;
                        });
                        
                        show_toastr('Success', '{{ __("Tag created successfully!") }}', 'success');
                    } else {
                        show_toastr('Error', response.message || '{{ __("Failed to create tag") }}', 'error');
                    }
                },
                error: function() {
                    show_toastr('Error', '{{ __("Failed to create tag") }}', 'error');
                }
            });
        }
    });
});
</script>
