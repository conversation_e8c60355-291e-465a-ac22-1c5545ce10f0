<?php

namespace App\Exports;

use App\Models\Lead;
use App\Models\Deal;
use App\Models\ContactGroup;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ContactExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $contacts = collect();

        // Get leads data
        $leads = Lead::where('created_by', Auth::user()->creatorId())
            ->with('contactGroup')
            ->get();

        foreach ($leads as $lead) {
            $contacts->push([
                'name' => $lead->name ?? '',
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead',
                'contact_type' => $lead->contact_type ?? '',
                'date_of_birth' => $lead->date_of_birth ?? '',
                'tags' => $lead->tags ?? '',
                'postal_code' => $lead->postal_code ?? '',
                'city' => $lead->city ?? '',
                'state' => $lead->state ?? '',
                'country' => $lead->country ?? '',
                'business_name' => $lead->business_name ?? '',
                'business_gst' => $lead->business_gst ?? '',
                'business_state' => $lead->business_state ?? '',
                'business_postal_code' => $lead->business_postal_code ?? '',
                'business_address' => $lead->business_address ?? '',
                'contact_group' => $lead->contactGroup ? $lead->contactGroup->name : '',
                'notes' => $lead->notes ?? '',
                'lead_value' => $lead->lead_value ?? '',
                'opportunity_info' => $lead->opportunity_info ?? '',
                'opportunity_description' => $lead->opportunity_description ?? '',
                'opportunity_source' => $lead->opportunity_source ?? '',
                'status' => $lead->status ?? '',
                'created_at' => $lead->created_at ? $lead->created_at->format('Y-m-d H:i:s') : '',
            ]);
        }

        // Get deals data
        $deals = Deal::where('created_by', Auth::user()->creatorId())->get();

        foreach ($deals as $deal) {
            $contacts->push([
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal',
                'contact_type' => '',
                'date_of_birth' => '',
                'tags' => '',
                'postal_code' => '',
                'city' => '',
                'state' => '',
                'country' => '',
                'business_name' => '',
                'business_gst' => '',
                'business_state' => '',
                'business_postal_code' => '',
                'business_address' => '',
                'contact_group' => '',
                'notes' => $deal->notes ?? '',
                'lead_value' => $deal->price ?? '',
                'opportunity_info' => '',
                'opportunity_description' => '',
                'opportunity_source' => '',
                'status' => $deal->status ?? '',
                'created_at' => $deal->created_at ? $deal->created_at->format('Y-m-d H:i:s') : '',
            ]);
        }

        return $contacts;
    }

    public function headings(): array
    {
        return [
            'Name',
            'Email',
            'Phone',
            'Type',
            'Contact Type',
            'Date of Birth',
            'Tags',
            'Postal Code',
            'City',
            'State',
            'Country',
            'Business Name',
            'Business GST',
            'Business State',
            'Business Postal Code',
            'Business Address',
            'Contact Group',
            'Notes',
            'Lead Value',
            'Opportunity Info',
            'Opportunity Description',
            'Opportunity Source',
            'Status',
            'Created At',
        ];
    }
}
