<?php echo e(Form::open(array('url' => 'leads', 'class'=>'needs-validation', 'novalidate', 'id' => 'create-lead-form'))); ?>

<div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
    
    <?php
        $plan= \App\Models\Utility::getChatGPTSettings();
    ?>
    <?php if($plan->chatgpt == 1): ?>
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="<?php echo e(route('generate',['lead'])); ?>"
           data-bs-placement="top" data-title="<?php echo e(__('Generate content with AI')); ?>">
            <i class="fas fa-robot"></i> <span><?php echo e(__('Generate with AI')); ?></span>
        </a>
    </div>
    <?php endif; ?>
    
    
    <!-- Basic Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3"><?php echo e(__('Basic Information')); ?></h6>
        </div>  
        <div class="col-6 form-group">
            <?php echo e(Form::label('name', __('Name'),['class'=>'form-label'])); ?><?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
            <?php echo e(Form::text('name', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter Name')))); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('email', __('Email'),['class'=>'form-label'])); ?><?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
            <?php echo e(Form::text('email', null, array('class' => 'form-control','required'=>'required' , 'placeholder' => __('Enter email')))); ?>

        </div>
        <div class="col-6 form-group">
            <?php if (isset($component)) { $__componentOriginal5d1845474bd0b0647eed674e26ea3910 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5d1845474bd0b0647eed674e26ea3910 = $attributes; } ?>
<?php $component = App\View\Components\Mobile::resolve(['label' => ''.e(__('Phone')).'','name' => 'phone','value' => ''.e(old('phone')).'','required' => true,'placeholder' => 'Enter Phone'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mobile'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Mobile::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5d1845474bd0b0647eed674e26ea3910)): ?>
<?php $attributes = $__attributesOriginal5d1845474bd0b0647eed674e26ea3910; ?>
<?php unset($__attributesOriginal5d1845474bd0b0647eed674e26ea3910); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5d1845474bd0b0647eed674e26ea3910)): ?>
<?php $component = $__componentOriginal5d1845474bd0b0647eed674e26ea3910; ?>
<?php unset($__componentOriginal5d1845474bd0b0647eed674e26ea3910); ?>
<?php endif; ?>
        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('subject', __('Subject'),['class'=>'form-label'])); ?>

            <?php echo e(Form::text('subject', old('subject'), ['class' => 'form-control', 'placeholder' => __('Enter Subject (optional)')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('user_id', __('User'),['class'=>'form-label'])); ?><?php if (isset($component)) { $__componentOriginalbba606fec37ea04333bc269e3e165587 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbba606fec37ea04333bc269e3e165587 = $attributes; } ?>
<?php $component = App\View\Components\Required::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('required'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Required::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $attributes = $__attributesOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__attributesOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbba606fec37ea04333bc269e3e165587)): ?>
<?php $component = $__componentOriginalbba606fec37ea04333bc269e3e165587; ?>
<?php unset($__componentOriginalbba606fec37ea04333bc269e3e165587); ?>
<?php endif; ?>
            <?php echo e(Form::select('user_id', $users,null, array('class' => 'form-control select','required'=>'required'))); ?>

            <div class="text-xs mt-1">
                <?php echo e(__('Create user here.')); ?> <a href="<?php echo e(route('users.index')); ?>"><b><?php echo e(__('Create user')); ?></b></a>
            </div>
        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('date_of_birth', __('Date of Birth'),['class'=>'form-label'])); ?>

            <?php echo e(Form::date('date_of_birth', old('date_of_birth'), ['class' => 'form-control', 'placeholder' => __('Select date')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('next_follow_up_date', __('Next Follow-Up Date'),['class'=>'form-label'])); ?>

            <?php echo e(Form::date('next_follow_up_date', old('next_follow_up_date'), ['class' => 'form-control', 'placeholder' => __('Select date')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('type', __('Type'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('type', [
                'lead' => __('Lead'),
                'customer' => __('Customer'),
            ], old('type'), ['class' => 'form-control select', 'placeholder' => __('Select Type')])); ?>

        </div>
    </div>

    <!-- Pipeline & Stage Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3"><?php echo e(__('Pipeline & Stage')); ?></h6>
        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('pipeline_id', __('Pipeline'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('pipeline_id', $pipelines ?? [], old('pipeline_id'), ['class' => 'form-control select', 'placeholder' => __('Select Pipeline')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('stage_id', __('Stage'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('stage_id', [], old('stage_id'), ['class' => 'form-control select', 'placeholder' => __('Select Stage'), 'id' => 'stage-select'])); ?>

            <small class="text-muted" id="stage-help-text"><?php echo e(__('Select a pipeline first to load stages')); ?></small>
        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('status', __('Status'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('status', [
                'cold' => __('Cold Lead'),
                'warm' => __('Warm Lead'),
                'hot' => __('Hot Lead'),
                'won' => __('Won Lead'),
                'lost' => __('Lost Lead')       
            ], old('status'), ['class' => 'form-control select', 'placeholder' => __('Select Status')])); ?>

        </div>
    </div>

    <!-- Opportunity Information Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3"><?php echo e(__('Opportunity Information')); ?></h6>
        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('opportunity_info', __('Opportunity Info'),['class'=>'form-label'])); ?>

            <?php echo e(Form::text('opportunity_info', old('opportunity_info'), ['class' => 'form-control', 'placeholder' => __('Enter Opportunity Info')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('opportunity_source', __('Opportunity Source'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('opportunity_source', [
                'website' => __('Website'),
                'referral' => __('Referral'),
                'social_media' => __('Social Media'),
                'email' => __('Email'),
                'phone' => __('Phone'),
                'advertisement' => __('Advertisement'),
                'other' => __('Other')
            ], old('opportunity_source'), ['class' => 'form-control select', 'placeholder' => __('Select Source')])); ?>

        </div>
        <div class="col-6 form-group">
            <?php echo e(Form::label('lead_value', __('Lead Value'),['class'=>'form-label'])); ?>

            <?php echo e(Form::number('lead_value', old('lead_value'), ['class' => 'form-control', 'placeholder' => __('Enter Value'), 'step' => '0.01', 'min' => '0'])); ?>

        </div>
        <div class="col-12 form-group">
            <?php echo e(Form::label('opportunity_description', __('Opportunity Description'),['class'=>'form-label'])); ?>

            <?php echo e(Form::textarea('opportunity_description', old('opportunity_description'), ['class' => 'form-control', 'rows' => '3', 'placeholder' => __('Enter Opportunity Description')])); ?>

        </div>
    </div>

    <!-- Tags/Labels Section -->
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-tags me-2"></i><?php echo e(__('Tags & Labels')); ?>

            </h6>
        </div>
        <div class="col-12 form-group">
            <?php echo e(Form::label('labels', __('Tags/Labels'),['class'=>'form-label'])); ?>

            <?php echo e(Form::select('labels[]', $labels ?? [], old('labels'), ['class' => 'form-control select', 'multiple' => 'multiple', 'id' => 'labels-select'])); ?>

            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                <?php echo e(__('You can select existing tags or type to create new ones. New tags will be created automatically.')); ?>

            </small>
            <!-- Debug info -->
            <?php if(isset($labels) && $labels->count() > 0): ?>
                <small class="text-info">
                    <i class="fas fa-database me-1"></i>
                    <?php echo e(__('Available labels:')); ?> <?php echo e($labels->count()); ?> <?php echo e(__('labels loaded from database')); ?>

                </small>
            <?php else: ?>
                <small class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <?php echo e(__('No labels found. You can create new ones by typing.')); ?>

                </small>
            <?php endif; ?>
        </div>
    </div>

    <!-- Custom Fields Section -->
    <?php if(isset($customFields) && $customFields->count() > 0): ?>
    <div class="row mb-3">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-cogs me-2"></i><?php echo e(__('Custom Fields')); ?>

            </h6>
        </div>
        <div class="col-12">
            <div class="row">
                <?php echo $__env->make('customFields.formBuilder', ['customFields' => $customFields, 'customFieldValues' => $customFieldValues ?? []], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn  btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Create')); ?>" class="btn  btn-primary">
</div>

<?php echo e(Form::close()); ?>


<style>
.select2-container--default .select2-results__option[aria-selected] {
    background-color: #e3f2fd;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2196f3;
    color: white;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #2196f3;
    border: 1px solid #2196f3;
    color: white;
    border-radius: 4px;
    padding-inline: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f0f0f0;
}

/* Stage loading styles */
#stage-select:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

#stage-help-text {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#stage-help-text i {
    font-size: 0.75rem;
}

/* Select2 styles for modal */
.select2-container {
    z-index: 9999;
}

.select2-dropdown {
    z-index: 9999;
}

.select2-container--default .select2-selection--multiple {
    min-height: 38px;
    border: 1px solid #ced4da;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #007bff;
    border: 1px solid #007bff;
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    padding-inline: 15px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 5px;
    font-weight: bold;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #f8f9fa;
}

/* Ensure clear button is always visible */
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    padding-right: 30px;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    font-weight: bold;
    font-size: 16px;
}

.select2-container--default .select2-selection--multiple .select2-selection__clear:hover {
    color: #333;
}
</style>

<script>
$(document).ready(function() {
    // Ensure Select2 is properly initialized in modal context
    $(document).on('shown.bs.modal', function() {
        // Reinitialize Select2 when modal is shown
        if ($('#labels-select').length) {
            $('#labels-select').select2('destroy');
            $('#labels-select').select2({
                placeholder: '<?php echo e(__("Select Tags/Labels")); ?>',
                allowClear: true,
                tags: true,
                width: '100%',
                dropdownParent: $('body'),
                minimumResultsForSearch: 0,
                closeOnSelect: false,
                
                createTag: function(params) {
                    if (params.term.trim() === '') {
                        return undefined;
                    }
                    
                    var existingOptions = $('#labels-select option');
                    for (var i = 0; i < existingOptions.length; i++) {
                        if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                            return undefined;
                        }
                    }
                    
                    return {
                        id: 'new_' + params.term,
                        text: params.term,
                        newTag: true
                    };
                },
                
                templateResult: function(data) {
                    if (data.newTag) {
                        return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
                    }
                    return data.text;
                },
                
                templateSelection: function(data) {
                    if (data.newTag) {
                        return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
                    }
                    return data.text;
                }
            }).on('select2:select', function(e) {
                if (e.params.data && e.params.data.newTag) {
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('success', 'New tag "' + e.params.data.text + '" will be created when you save the lead.', 'success');
                    }
                }
            }).on('select2:select select2:unselect', function() {
                // Show/hide clear button based on selections
                var $container = $(this).next('.select2-container');
                var $clearButton = $container.find('.select2-selection__clear');
                var hasSelections = $(this).val() && $(this).val() && $(this).val().length > 0;
                
                if (hasSelections) {
                    $clearButton.show();
                } else {
                    $clearButton.hide();
                }
            });
        }
    });
    
    // Initialize Select2 for labels with tag creation functionality
    $('#labels-select').select2({
        placeholder: '<?php echo e(__("Select Tags/Labels")); ?>',
        allowClear: true,
        tags: true, // Enable tag creation
        width: '100%',
        dropdownParent: $('body'), // Ensure dropdown appears above modal
        minimumResultsForSearch: 0,
        closeOnSelect: false,
        
        // Debug: Log the options being loaded
        initSelection: function(element, callback) {
            console.log('Select2 labels options:', $(element).find('option'));
            console.log('Labels data:', <?php echo json_encode($labels ?? [], 15, 512) ?>);
        },
        
        createTag: function(params) {
            // Check if the term is empty
            if (params.term.trim() === '') {
                return undefined;
            }
            
            // Check if the term already exists
            var existingOptions = $('#labels-select option');
            for (var i = 0; i < existingOptions.length; i++) {
                if (existingOptions[i].text.toLowerCase() === params.term.toLowerCase()) {
                    return undefined;
                }
            }
            
            return {
                id: 'new_' + params.term, // Prefix with 'new_' to identify new tags
                text: params.term,
                newTag: true
            };
        },
        
        templateResult: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + ' <small class="text-muted">(new)</small></span>');
            }
            return data.text;
        },
        
        templateSelection: function(data) {
            if (data.newTag) {
                return $('<span><i class="fas fa-plus-circle text-success me-1"></i>' + data.text + '</span>');
            }
            return data.text;
        }
    }).on('select2:select', function(e) {
        // Show notification when a new tag is created
        if (e.params.data && e.params.data.newTag) {
            if (typeof show_toastr !== 'undefined') {
                show_toastr('success', 'New tag "' + e.params.data.text + '" will be created when you save the lead.', 'success');
            }
        }
    }).on('select2:open', function() {
        console.log('Select2 dropdown opened');
        console.log('Available options:', $('#labels-select option').length);
    }).on('select2:select select2:unselect', function() {
        // Show/hide clear button based on selections
        var $container = $(this).next('.select2-container');
        var $clearButton = $container.find('.select2-selection__clear');
        var hasSelections = $(this).val() && $(this).val().length > 0;
        
        if (hasSelections) {
            $clearButton.show();
        } else {
            $clearButton.hide();
        }
    });
    
    // Handle pipeline change to load stages
    $('select[name="pipeline_id"]').on('change', function() {
        var pipelineId = $(this).val();
        var stageSelect = $('select[name="stage_id"]');
        var stageHelpText = $('#stage-help-text');
        
        if (pipelineId) {
            // Clear current stages
            stageSelect.empty().append('<option value=""><?php echo e(__("Select Stage")); ?></option>');
            
            // Show loading state
            stageSelect.prop('disabled', true);
            stageHelpText.html('<i class="fas fa-spinner fa-spin me-1"></i><?php echo e(__("Loading stages...")); ?>');
            
            // Load stages for selected pipeline using direct database query
            var url = '<?php echo e(route("leads.pipelineStages")); ?>';
            console.log('Calling URL:', url, 'with pipeline_id:', pipelineId);
            
            $.ajax({
                url: url,
                type: 'GET',
                data: {
                    pipeline_id: pipelineId
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Stages loaded:', response);
                    
                    if (response.success && response.stages && response.stages.length > 0) {
                        response.stages.forEach(function(stage) {
                            stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                        });
                        
                        stageHelpText.html('<i class="fas fa-check-circle text-success me-1"></i>' + response.stages.length + ' <?php echo e(__("stages available")); ?>');
                        
                        // Show success message
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('success', response.stages.length + ' stages loaded for this pipeline.', 'success');
                        }
                    } else {
                        stageSelect.append('<option value="" disabled><?php echo e(__("No stages found for this pipeline")); ?></option>');
                        stageHelpText.html('<i class="fas fa-exclamation-triangle text-warning me-1"></i><?php echo e(__("No stages found for this pipeline")); ?>');
                        
                        if (typeof show_toastr !== 'undefined') {
                            show_toastr('warning', 'No stages found for this pipeline.', 'warning');
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Error loading stages:', xhr);
                    console.error('Status:', xhr.status);
                    console.error('Response:', xhr.responseText);
                    
                    var errorMessage = '<?php echo e(__("Error loading stages")); ?>';
                    if (xhr.status === 403) {
                        errorMessage = '<?php echo e(__("Permission denied")); ?>';
                    } else if (xhr.status === 404) {
                        errorMessage = '<?php echo e(__("Pipeline not found")); ?>';
                    } else if (xhr.status === 500) {
                        errorMessage = '<?php echo e(__("Server error")); ?>';
                    }
                    
                    stageSelect.append('<option value="" disabled>' + errorMessage + '</option>');
                    stageHelpText.html('<i class="fas fa-times-circle text-danger me-1"></i>' + errorMessage);
                    
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', errorMessage, 'error');
                    }
                },
                complete: function() {
                    // Re-enable the select
                    stageSelect.prop('disabled', false);
                }
            });
        } else {
            stageSelect.empty().append('<option value=""><?php echo e(__("Select Stage")); ?></option>');
            stageHelpText.html('<?php echo e(__("Select a pipeline first to load stages")); ?>');
        }
    });
    
    // Handle form submission with AJAX
    $('#create-lead-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = form.find('input[type="submit"]');
        var originalText = submitBtn.val();
        
        // Disable submit button and show loading
        submitBtn.prop('disabled', true).val('<?php echo e(__("Creating...")); ?>');
        
        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Lead created successfully:', response);
                
                if (response.success) {
                    // Show success message
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('success', response.message || '<?php echo e(__("Lead created successfully!")); ?>', 'success');
                    }
                    
                    // Close modal
                    $('.modal').modal('hide');
                    
                    // Reload page or update leads list
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', response.message || '<?php echo e(__("Error creating lead")); ?>', 'error');
                    }
                }
            },
            error: function(xhr) {
                console.error('Error creating lead:', xhr);
                
                if (xhr.status === 422) {
                    // Validation errors
                    var errors = xhr.responseJSON.errors;
                    if (errors) {
                        $.each(errors, function(field, messages) {
                            var input = $('[name="' + field + '"]');
                            input.addClass('is-invalid');
                            input.after('<div class="invalid-feedback">' + messages[0] + '</div>');
                        });
                    }
                    
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', '<?php echo e(__("Please fix the validation errors")); ?>', 'error');
                    }
                } else if (xhr.status === 403) {
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', '<?php echo e(__("Permission denied")); ?>', 'error');
                    }
                } else {
                    // Show generic error
                    if (typeof show_toastr !== 'undefined') {
                        show_toastr('error', '<?php echo e(__("Server error occurred. Please try again.")); ?>', 'error');
                    }
                }
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).val(originalText);
            }
        });
    });
});
</script>

<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/leads/create.blade.php ENDPATH**/ ?>