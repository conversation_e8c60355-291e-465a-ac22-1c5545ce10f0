{{ Form::open(array('route' => ['leads.labels.store',$lead->id])) }}
<div class="modal-body">
    <!-- Existing Tags Section -->
    <div class="row">
        <div class="col-12 form-group">
            <h6 class="mb-3">{{ __('Select Existing Tags') }}</h6>
            <div class="row gutters-xs">
                @if(count($tags) > 0)
                    @foreach ($tags as $tag)
                        <div class="col-12 custom-control custom-checkbox mt-2 mb-2 d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                {{ Form::checkbox('tags[]',$tag->id,(array_key_exists($tag->id,$selected))?true:false,['class' => 'form-check-input','id'=>'tags_'.$tag->id]) }}
                                {{ Form::label('tags_'.$tag->id, ucfirst($tag->name),['class'=>'custom-control-label ml-4 text-white p-2 px-3 rounded status_badge badge bg-primary']) }}
                            </div>
                            <div class="d-flex gap-1">
                                <button type="button" class="btn btn-sm btn-outline-primary edit-tag-btn"
                                    data-tag-id="{{ $tag->id }}"
                                    data-tag-name="{{ $tag->name }}"
                                    title="{{ __('Edit Tag') }}">
                                    <i class="ti ti-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-tag-btn"
                                    data-tag-id="{{ $tag->id }}"
                                    data-tag-name="{{ $tag->name }}"
                                    title="{{ __('Delete Tag') }}">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="col-12">
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="ti ti-tag text-muted" style="font-size: 3rem;"></i>
                            </div>
                            <h6 class="text-muted">{{ __('No Tags Available') }}</h6>
                            <p class="text-muted small">{{ __('Create your first tag using the form below to organize your leads better.') }}</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Edit Tag Section (Hidden by default) -->
    <div id="edit-tag-section" style="display: none;">
        <hr class="my-4">
        <div class="row">
            <div class="col-12">
                <h6 class="mb-3">{{ __('Edit Tag') }}</h6>

                <div class="form-group mb-3">
                    {{ Form::label('edit_tag_name', __('Tag Name'), ['class' => 'form-label']) }}
                    {{ Form::text('edit_tag_name', '', ['class' => 'form-control', 'placeholder' => __('Enter Tag Name'), 'id' => 'edit_tag_name']) }}
                </div>

                <div class="d-flex gap-2 mb-3">
                    <button type="button" class="btn btn-success btn-sm" id="save-edit-tag">{{ __('Save Changes') }}</button>
                    <button type="button" class="btn btn-secondary btn-sm" id="cancel-edit-tag">{{ __('Cancel') }}</button>
                </div>

                {{ Form::hidden('edit_tag_id', '', ['id' => 'edit_tag_id']) }}
            </div>
        </div>
    </div>

    <!-- Create New Tag Section -->
    <hr class="my-4">
    <div class="row" id="create-tag-section">
        <div class="col-12">
            <h6 class="mb-3">{{ __('Create New Tag') }}</h6>

            <div class="form-group mb-3">
                {{ Form::label('new_tag_name', __('Tag Name'), ['class' => 'form-label']) }}
                {{ Form::text('new_tag_name', '', ['class' => 'form-control', 'placeholder' => __('Enter Tag Name'), 'id' => 'new_tag_name']) }}
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Save')}}" class="btn btn-primary">
</div>

{{Form::close()}}

<script>
$(document).ready(function() {
    // Handle form submission
    $('form').on('submit', function(e) {
        var newTagName = $('#new_tag_name').val().trim();

        // If user is creating a new tag, validate the fields
        if (newTagName) {
            if (!newTagName) {
                e.preventDefault();
                show_toastr('error', '{{ __("Please enter a tag name") }}');
                return false;
            }
        }
    });

    // Handle edit tag button click
    $('.edit-tag-btn').on('click', function() {
        var tagId = $(this).data('tag-id');
        var tagName = $(this).data('tag-name');

        // Show edit section and hide create section
        $('#edit-tag-section').show();
        $('#create-tag-section').hide();

        // Populate edit form
        $('#edit_tag_id').val(tagId);
        $('#edit_tag_name').val(tagName);
    });

    // Handle cancel edit
    $('#cancel-edit-tag').on('click', function() {
        $('#edit-tag-section').hide();
        $('#create-tag-section').show();
        clearEditForm();
    });

    // Handle save edit tag
    $('#save-edit-tag').on('click', function() {
        var tagId = $('#edit_tag_id').val();
        var tagName = $('#edit_tag_name').val().trim();

        if (!tagName) {
            show_toastr('error', '{{ __("Please enter a tag name") }}');
            return;
        }

        // Send AJAX request to update tag
        $.ajax({
            url: '{{ route("tags.update", ":id") }}'.replace(':id', tagId),
            type: 'PUT',
            data: {
                name: tagName,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                show_toastr('success', '{{ __("Tag updated successfully!") }}');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                show_toastr('error', errorMessage);
            }
        });
    });

    // Handle delete tag button click
    $('.delete-tag-btn').on('click', function() {
        var tagId = $(this).data('tag-id');
        var tagName = $(this).data('tag-name');

        if (confirm('{{ __("Are you sure you want to delete the tag") }} "' + tagName + '"?')) {
            $.ajax({
                url: '{{ route("tags.destroy", ":id") }}'.replace(':id', tagId),
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    show_toastr('success', '{{ __("Tag deleted successfully!") }}');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    show_toastr('error', errorMessage);
                }
            });
        }
    });

    // Clear edit form
    function clearEditForm() {
        $('#edit_tag_id').val('');
        $('#edit_tag_name').val('');
    }

    // Clear new tag fields when modal is closed
    $('.modal').on('hidden.bs.modal', function() {
        $('#new_tag_name').val('');
        $('#edit-tag-section').hide();
        $('#create-tag-section').show();
        clearEditForm();
    });
});
</script>

